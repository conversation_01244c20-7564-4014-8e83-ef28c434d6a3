#!/usr/bin/env python3
#
# Cross Platform and Multi Architecture Advanced Binary Emulation Framework
#

EFI_MAX_BIT = (1 << 63)
EFI_SUCCESS = 0

EFI_LOAD_ERROR           = EFI_MAX_BIT |  1
EFI_INVALID_PARAMETER    = EFI_MAX_BIT |  2
EFI_UNSUPPORTED          = EFI_MAX_BIT |  3
EFI_BAD_BUFFER_SIZE      = EFI_MAX_BIT |  4
EFI_BUFFER_TOO_SMALL     = EFI_MAX_BIT |  5
EFI_NOT_READY            = EFI_MAX_BIT |  6
EFI_DEVICE_ERROR         = EFI_MAX_BIT |  7
EFI_WRITE_PROTECTED      = EFI_MAX_BIT |  8
EFI_OUT_OF_RESOURCES     = EFI_MAX_BIT |  9
EFI_VOLUME_CORRUPTED     = EFI_MAX_BIT | 10
EFI_VOLUME_FULL          = EFI_MAX_BIT | 11
EFI_NO_MEDIA             = EFI_MAX_BIT | 12
EFI_MEDIA_CHANGED        = EFI_MAX_BIT | 13
EFI_NOT_FOUND            = EFI_MAX_BIT | 14
EFI_ACCESS_DENIED        = EFI_MAX_BIT | 15
EFI_NO_RESPONSE          = EFI_MAX_BIT | 16
EFI_NO_MAPPING           = EFI_MAX_BIT | 17
EFI_TIMEOUT              = EFI_MAX_BIT | 18
EFI_NOT_STARTED          = EFI_MAX_BIT | 19
EFI_ALREADY_STARTED      = EFI_MAX_BIT | 20
EFI_ABORTED              = EFI_MAX_BIT | 21
EFI_ICMP_ERROR           = EFI_MAX_BIT | 22
EFI_TFTP_ERROR           = EFI_MAX_BIT | 23
EFI_PROTOCOL_ERROR       = EFI_MAX_BIT | 24
EFI_INCOMPATIBLE_VERSION = EFI_MAX_BIT | 25
EFI_SECURITY_VIOLATION   = EFI_MAX_BIT | 26
EFI_CRC_ERROR            = EFI_MAX_BIT | 27
EFI_END_OF_MEDIA         = EFI_MAX_BIT | 28
EFI_END_OF_FILE          = EFI_MAX_BIT | 31
EFI_INVALID_LANGUAGE     = EFI_MAX_BIT | 32

EFI_WARN_UNKNOWN_GLYPH    = EFI_MAX_BIT | 1
EFI_WARN_DELETE_FAILURE   = EFI_MAX_BIT | 2
EFI_WARN_WRITE_FAILURE    = EFI_MAX_BIT | 3
EFI_WARN_BUFFER_TOO_SMALL = EFI_MAX_BIT | 4

# @see: MdePkg\Include\Base.h
def EFI_ERROR(status: int) -> bool:
    return (status & EFI_MAX_BIT) != 0
