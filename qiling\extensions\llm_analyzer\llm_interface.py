#!/usr/bin/env python3
# 
# Cross Platform and Multi Architecture Advanced Binary Emulation Framework
# LLM Interface Layer
#

import json
import requests
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from .config import LLMConfig


class LLMInterface(ABC):
    """Abstract base class for LLM interfaces"""
    
    def __init__(self, config: LLMConfig):
        self.config = config
    
    @abstractmethod
    def generate(self, prompt: str, system_prompt: Optional[str] = None) -> str:
        """Generate response from LLM"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if LLM backend is available"""
        pass


class OllamaInterface(LLMInterface):
    """Interface for Ollama local LLM"""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.api_url = f"{config.base_url}/api/generate"
    
    def generate(self, prompt: str, system_prompt: Optional[str] = None) -> str:
        """Generate response using Ollama API"""
        try:
            payload = {
                "model": self.config.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": self.config.temperature,
                    "top_p": self.config.top_p,
                    "num_predict": self.config.max_tokens
                }
            }
            
            if system_prompt:
                payload["system"] = system_prompt
            
            response = requests.post(
                self.api_url,
                json=payload,
                timeout=300  # 5 minutes timeout
            )
            response.raise_for_status()
            
            result = response.json()
            return result.get("response", "")
            
        except Exception as e:
            raise RuntimeError(f"Ollama API error: {str(e)}")
    
    def is_available(self) -> bool:
        """Check if Ollama is running and model is available"""
        try:
            # Check if Ollama is running
            response = requests.get(f"{self.config.base_url}/api/tags", timeout=5)
            if response.status_code != 200:
                return False
            
            # Check if model is available
            models = response.json().get("models", [])
            model_names = [model["name"] for model in models]
            
            return self.config.model in model_names
            
        except Exception:
            return False


class LlamaCppInterface(LLMInterface):
    """Interface for llama.cpp server"""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.api_url = f"{config.base_url}/completion"
    
    def generate(self, prompt: str, system_prompt: Optional[str] = None) -> str:
        """Generate response using llama.cpp server"""
        try:
            full_prompt = prompt
            if system_prompt:
                full_prompt = f"System: {system_prompt}\n\nUser: {prompt}\n\nAssistant:"
            
            payload = {
                "prompt": full_prompt,
                "temperature": self.config.temperature,
                "top_p": self.config.top_p,
                "n_predict": self.config.max_tokens,
                "stop": ["User:", "System:"]
            }
            
            response = requests.post(
                self.api_url,
                json=payload,
                timeout=300
            )
            response.raise_for_status()
            
            result = response.json()
            return result.get("content", "")
            
        except Exception as e:
            raise RuntimeError(f"llama.cpp API error: {str(e)}")
    
    def is_available(self) -> bool:
        """Check if llama.cpp server is available"""
        try:
            response = requests.get(f"{self.config.base_url}/health", timeout=5)
            return response.status_code == 200
        except Exception:
            return False


class OpenAICompatibleInterface(LLMInterface):
    """Interface for OpenAI-compatible APIs (like LM Studio, vLLM, etc.)"""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.api_url = f"{config.base_url}/v1/chat/completions"
    
    def generate(self, prompt: str, system_prompt: Optional[str] = None) -> str:
        """Generate response using OpenAI-compatible API"""
        try:
            messages = []
            
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            
            messages.append({"role": "user", "content": prompt})
            
            headers = {"Content-Type": "application/json"}
            if self.config.api_key:
                headers["Authorization"] = f"Bearer {self.config.api_key}"
            
            payload = {
                "model": self.config.model,
                "messages": messages,
                "temperature": self.config.temperature,
                "top_p": self.config.top_p,
                "max_tokens": self.config.max_tokens
            }
            
            response = requests.post(
                self.api_url,
                json=payload,
                headers=headers,
                timeout=300
            )
            response.raise_for_status()
            
            result = response.json()
            return result["choices"][0]["message"]["content"]
            
        except Exception as e:
            raise RuntimeError(f"OpenAI-compatible API error: {str(e)}")
    
    def is_available(self) -> bool:
        """Check if API is available"""
        try:
            # Try to get models list
            headers = {}
            if self.config.api_key:
                headers["Authorization"] = f"Bearer {self.config.api_key}"
            
            response = requests.get(
                f"{self.config.base_url}/v1/models",
                headers=headers,
                timeout=5
            )
            return response.status_code == 200
        except Exception:
            return False


def create_llm_interface(config: LLMConfig) -> LLMInterface:
    """Factory function to create appropriate LLM interface"""
    
    interfaces = {
        "ollama": OllamaInterface,
        "llamacpp": LlamaCppInterface,
        "openai-compatible": OpenAICompatibleInterface
    }
    
    interface_class = interfaces.get(config.backend)
    if not interface_class:
        raise ValueError(f"Unsupported LLM backend: {config.backend}")
    
    return interface_class(config)
