#!/usr/bin/env python3
# 
# Cross Platform and Multi Architecture Advanced Binary Emulation Framework
# Configuration Management System
#

import os
import json
import shutil
from typing import Dict, Any, List, Optional
from pathlib import Path

from .config import LLMConfig, DEFAULT_CONFIG_DIR, DEFAULT_CONFIG_PATH


class ConfigManager:
    """Manage LLM analyzer configurations"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """Initialize configuration manager
        
        Args:
            config_dir: Directory to store configurations. If None, uses default.
        """
        self.config_dir = Path(config_dir or DEFAULT_CONFIG_DIR)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Predefined configuration templates
        self.templates = {
            "ollama_local": {
                "backend": "ollama",
                "model": "llama3.2:3b",
                "base_url": "http://localhost:11434",
                "temperature": 0.1,
                "max_tokens": 4096,
                "analysis_depth": "standard",
                "output_format": "markdown"
            },
            "ollama_large": {
                "backend": "ollama",
                "model": "llama3.1:8b",
                "base_url": "http://localhost:11434",
                "temperature": 0.05,
                "max_tokens": 8192,
                "analysis_depth": "deep",
                "output_format": "markdown"
            },
            "llamacpp_local": {
                "backend": "llamacpp",
                "model": "llama-3.2-3b-instruct",
                "base_url": "http://localhost:8080",
                "temperature": 0.1,
                "max_tokens": 4096,
                "analysis_depth": "standard",
                "output_format": "markdown"
            },
            "lmstudio": {
                "backend": "openai-compatible",
                "model": "llama-3.2-3b-instruct",
                "base_url": "http://localhost:1234",
                "temperature": 0.1,
                "max_tokens": 4096,
                "analysis_depth": "standard",
                "output_format": "markdown"
            },
            "security_focused": {
                "backend": "ollama",
                "model": "llama3.2:3b",
                "base_url": "http://localhost:11434",
                "temperature": 0.05,  # Lower temperature for more focused analysis
                "max_tokens": 6144,
                "analysis_depth": "deep",
                "include_security_assessment": True,
                "include_code_analysis": True,
                "include_recommendations": True,
                "output_format": "markdown",
                "verbose_output": True
            },
            "fast_analysis": {
                "backend": "ollama",
                "model": "llama3.2:1b",  # Smaller model for speed
                "base_url": "http://localhost:11434",
                "temperature": 0.2,
                "max_tokens": 2048,
                "analysis_depth": "basic",
                "include_security_assessment": True,
                "include_code_analysis": False,
                "include_recommendations": False,
                "output_format": "json",
                "verbose_output": False
            }
        }
    
    def create_config(self, name: str, template: str = "ollama_local", 
                     custom_settings: Optional[Dict[str, Any]] = None) -> LLMConfig:
        """Create a new configuration
        
        Args:
            name: Configuration name
            template: Template to base configuration on
            custom_settings: Custom settings to override template
            
        Returns:
            Created LLMConfig instance
        """
        if template not in self.templates:
            available = list(self.templates.keys())
            raise ValueError(f"Unknown template '{template}'. Available: {available}")
        
        # Start with template
        config_data = self.templates[template].copy()
        
        # Apply custom settings
        if custom_settings:
            config_data.update(custom_settings)
        
        # Create config instance
        config = LLMConfig(**config_data)
        
        # Save to file
        config_path = self.config_dir / f"{name}.json"
        config.save_to_file(str(config_path))
        
        return config
    
    def load_config(self, name: str) -> LLMConfig:
        """Load configuration by name
        
        Args:
            name: Configuration name
            
        Returns:
            Loaded LLMConfig instance
        """
        config_path = self.config_dir / f"{name}.json"
        
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration '{name}' not found at {config_path}")
        
        return LLMConfig.from_file(str(config_path))
    
    def list_configs(self) -> List[str]:
        """List available configuration names
        
        Returns:
            List of configuration names
        """
        configs = []
        for config_file in self.config_dir.glob("*.json"):
            configs.append(config_file.stem)
        return sorted(configs)
    
    def delete_config(self, name: str) -> bool:
        """Delete a configuration
        
        Args:
            name: Configuration name to delete
            
        Returns:
            True if deleted, False if not found
        """
        config_path = self.config_dir / f"{name}.json"
        
        if config_path.exists():
            config_path.unlink()
            return True
        return False
    
    def get_template_info(self, template_name: str) -> Dict[str, Any]:
        """Get information about a configuration template
        
        Args:
            template_name: Name of the template
            
        Returns:
            Template information dictionary
        """
        if template_name not in self.templates:
            raise ValueError(f"Unknown template '{template_name}'")
        
        template_data = self.templates[template_name].copy()
        
        # Add description based on template name
        descriptions = {
            "ollama_local": "Standard Ollama setup with 3B model for balanced performance",
            "ollama_large": "Ollama with larger 8B model for more detailed analysis",
            "llamacpp_local": "Local llama.cpp server setup",
            "lmstudio": "LM Studio compatible configuration",
            "security_focused": "Optimized for security analysis with detailed output",
            "fast_analysis": "Quick analysis with smaller model for speed"
        }
        
        return {
            "name": template_name,
            "description": descriptions.get(template_name, "No description available"),
            "settings": template_data
        }
    
    def list_templates(self) -> List[Dict[str, Any]]:
        """List all available configuration templates
        
        Returns:
            List of template information dictionaries
        """
        return [self.get_template_info(name) for name in self.templates.keys()]
    
    def export_config(self, name: str, export_path: str) -> None:
        """Export configuration to a file
        
        Args:
            name: Configuration name to export
            export_path: Path to export the configuration
        """
        config_path = self.config_dir / f"{name}.json"
        
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration '{name}' not found")
        
        shutil.copy2(config_path, export_path)
    
    def import_config(self, name: str, import_path: str) -> LLMConfig:
        """Import configuration from a file
        
        Args:
            name: Name to give the imported configuration
            import_path: Path to import the configuration from
            
        Returns:
            Imported LLMConfig instance
        """
        if not os.path.exists(import_path):
            raise FileNotFoundError(f"Import file not found: {import_path}")
        
        # Load and validate the configuration
        config = LLMConfig.from_file(import_path)
        
        # Save with new name
        config_path = self.config_dir / f"{name}.json"
        config.save_to_file(str(config_path))
        
        return config
    
    def validate_config(self, config: LLMConfig) -> Dict[str, Any]:
        """Validate a configuration
        
        Args:
            config: Configuration to validate
            
        Returns:
            Validation results dictionary
        """
        results = {
            "valid": True,
            "warnings": [],
            "errors": []
        }
        
        # Check required fields
        if not config.backend:
            results["errors"].append("Backend is required")
            results["valid"] = False
        
        if not config.model:
            results["errors"].append("Model is required")
            results["valid"] = False
        
        if not config.base_url:
            results["errors"].append("Base URL is required")
            results["valid"] = False
        
        # Check value ranges
        if not (0.0 <= config.temperature <= 2.0):
            results["warnings"].append("Temperature should be between 0.0 and 2.0")
        
        if config.max_tokens < 100:
            results["warnings"].append("Max tokens seems very low (< 100)")
        elif config.max_tokens > 32000:
            results["warnings"].append("Max tokens seems very high (> 32000)")
        
        if not (0.0 <= config.top_p <= 1.0):
            results["warnings"].append("Top-p should be between 0.0 and 1.0")
        
        # Check backend-specific settings
        if config.backend == "openai-compatible" and not config.api_key:
            results["warnings"].append("API key might be required for OpenAI-compatible backends")
        
        return results
    
    def get_config_status(self, name: str) -> Dict[str, Any]:
        """Get status information for a configuration
        
        Args:
            name: Configuration name
            
        Returns:
            Status information dictionary
        """
        try:
            config = self.load_config(name)
            validation = self.validate_config(config)
            
            # Try to test the configuration
            from .llm_interface import create_llm_interface
            try:
                interface = create_llm_interface(config)
                available = interface.is_available()
            except Exception as e:
                available = False
                validation["errors"].append(f"Interface test failed: {str(e)}")
            
            return {
                "name": name,
                "exists": True,
                "valid": validation["valid"],
                "available": available,
                "validation": validation,
                "config": config.to_dict()
            }
            
        except FileNotFoundError:
            return {
                "name": name,
                "exists": False,
                "valid": False,
                "available": False,
                "error": "Configuration not found"
            }
        except Exception as e:
            return {
                "name": name,
                "exists": True,
                "valid": False,
                "available": False,
                "error": str(e)
            }
