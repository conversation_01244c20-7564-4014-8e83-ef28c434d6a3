#!/usr/bin/env python3
# 
# Cross Platform and Multi Architecture Advanced Binary Emulation Framework
# LLM-powered Report Analyzer
#

import json
import time
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

from .config import LLMConfig, get_default_config
from .llm_interface import create_llm_interface, LLMInterface
from .templates import get_template, list_templates, AnalysisTemplate
from ..report import generate_report


class QlLLMAnalyzer:
    """Main LLM-powered analysis engine for Qiling reports"""
    
    def __init__(self, config: Optional[LLMConfig] = None):
        """Initialize the LLM analyzer
        
        Args:
            config: LLM configuration. If None, uses default config.
        """
        self.config = config or get_default_config()
        self.llm_interface: Optional[LLMInterface] = None
        self._initialize_llm()
    
    def _initialize_llm(self) -> None:
        """Initialize LLM interface"""
        try:
            self.llm_interface = create_llm_interface(self.config)
            if not self.llm_interface.is_available():
                raise RuntimeError(f"LLM backend '{self.config.backend}' is not available")
        except Exception as e:
            print(f"Warning: Failed to initialize LLM interface: {e}")
            self.llm_interface = None
    
    def is_ready(self) -> bool:
        """Check if analyzer is ready to use"""
        return self.llm_interface is not None and self.llm_interface.is_available()
    
    def analyze_qiling_instance(self, ql, template_name: str = "comprehensive_analysis") -> Dict[str, Any]:
        """Analyze a Qiling instance directly
        
        Args:
            ql: Qiling instance after execution
            template_name: Analysis template to use
            
        Returns:
            Analysis results dictionary
        """
        # Generate report from Qiling instance
        report_data = generate_report(ql)
        
        # Analyze the report
        return self.analyze_report(report_data, template_name)
    
    def analyze_report(self, report_data: Dict[str, Any], template_name: str = "comprehensive_analysis") -> Dict[str, Any]:
        """Analyze a Qiling report using LLM
        
        Args:
            report_data: Report data from generate_report()
            template_name: Analysis template to use
            
        Returns:
            Analysis results dictionary
        """
        if not self.is_ready():
            raise RuntimeError("LLM analyzer is not ready. Check configuration and backend availability.")
        
        # Get analysis template
        template = get_template(template_name)
        
        # Prepare report data for analysis
        processed_data = self._process_report_data(report_data)
        
        # Generate analysis prompt
        user_prompt = template.format_prompt(processed_data)
        
        # Get LLM analysis
        start_time = time.time()
        try:
            analysis_text = self.llm_interface.generate(
                prompt=user_prompt,
                system_prompt=template.system_prompt
            )
            analysis_time = time.time() - start_time
            
        except Exception as e:
            raise RuntimeError(f"LLM analysis failed: {str(e)}")
        
        # Prepare results
        results = {
            "metadata": {
                "analysis_timestamp": datetime.now().isoformat(),
                "template_used": template_name,
                "llm_backend": self.config.backend,
                "llm_model": self.config.model,
                "analysis_time_seconds": round(analysis_time, 2),
                "config": self.config.to_dict()
            },
            "original_report": report_data if self.config.include_raw_data else None,
            "processed_data": processed_data if self.config.include_raw_data else None,
            "analysis": {
                "template_info": {
                    "name": template.name,
                    "description": template.description,
                    "type": template.analysis_type
                },
                "content": analysis_text,
                "prompt_used": user_prompt if self.config.include_raw_data else None
            }
        }
        
        return results

    def _process_report_data(self, report_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process and format report data for LLM analysis"""

        processed = {
            "filename": str(report_data.get("filename", "Unknown")),
            "arch": str(report_data.get("arch", "Unknown")),
            "os": str(report_data.get("os", "Unknown")),
            "rootfs": str(report_data.get("rootfs", "Unknown")),
            "env_summary": self._format_env_data(report_data.get("env", {})),
            "profile_summary": self._format_profile_data(report_data.get("profile", {})),
            "syscalls_summary": self._format_syscalls_data(report_data),
            "strings_summary": self._format_strings_data(report_data.get("strings", [])),
            "registry_summary": self._format_registry_data(report_data),
            "symbols_summary": self._format_symbols_data(report_data),
            "network_indicators": self._extract_network_indicators(report_data),
            "system_interactions": self._format_system_interactions(report_data),
            "entry_point": self._get_entry_point(report_data)
        }

        return processed

    def _format_env_data(self, env_data: Dict[str, Any]) -> str:
        """Format environment data"""
        if not env_data:
            return "No environment data available"

        env_items = []
        for key, value in env_data.items():
            env_items.append(f"{key}={value}")

        return "\n".join(env_items[:10])  # Limit to first 10 items

    def _format_profile_data(self, profile_data: Dict[str, Any]) -> str:
        """Format profile configuration data"""
        if not profile_data:
            return "No profile data available"

        profile_items = []
        for key, value in profile_data.items():
            profile_items.append(f"{key}: {value}")

        return "\n".join(profile_items)

    def _format_syscalls_data(self, report_data: Dict[str, Any]) -> str:
        """Format syscall data for analysis"""
        # Try to get syscalls from different possible locations
        syscalls = None

        # Check if it's in the report directly
        if "syscalls" in report_data:
            syscalls = report_data["syscalls"]
        elif "api" in report_data:
            syscalls = report_data["api"]

        if not syscalls:
            return "No syscall data available"

        syscall_summary = []
        syscall_count = {}

        for syscall_name, calls in syscalls.items():
            if isinstance(calls, list):
                count = len(calls)
                syscall_count[syscall_name] = count

                # Add details for first few calls
                if count > 0 and len(syscall_summary) < 20:  # Limit output
                    first_call = calls[0] if calls else {}
                    params = first_call.get("params", {})
                    retval = first_call.get("retval", "N/A")

                    syscall_summary.append(
                        f"{syscall_name} (called {count}x): params={params}, retval={retval}"
                    )

        # Add summary of most frequent syscalls
        if syscall_count:
            sorted_syscalls = sorted(syscall_count.items(), key=lambda x: x[1], reverse=True)
            top_syscalls = sorted_syscalls[:10]

            summary_text = "Top System Calls:\n"
            for name, count in top_syscalls:
                summary_text += f"- {name}: {count} calls\n"

            if syscall_summary:
                summary_text += "\nDetailed Call Examples:\n" + "\n".join(syscall_summary)

            return summary_text

        return "No syscall data could be processed"

    def _format_strings_data(self, strings_data: List[str]) -> str:
        """Format extracted strings data"""
        if not strings_data:
            return "No strings extracted"

        # Filter and categorize strings
        interesting_strings = []
        urls = []
        file_paths = []
        registry_keys = []

        for string in strings_data[:100]:  # Limit to first 100 strings
            string = str(string).strip()
            if len(string) < 3:  # Skip very short strings
                continue

            if string.startswith(('http://', 'https://', 'ftp://')):
                urls.append(string)
            elif '\\' in string and ('HKEY' in string or 'SOFTWARE' in string.upper()):
                registry_keys.append(string)
            elif ('\\' in string or '/' in string) and ('.' in string):
                file_paths.append(string)
            else:
                interesting_strings.append(string)

        result = []
        if urls:
            result.append(f"URLs found: {', '.join(urls[:5])}")
        if file_paths:
            result.append(f"File paths: {', '.join(file_paths[:5])}")
        if registry_keys:
            result.append(f"Registry keys: {', '.join(registry_keys[:5])}")
        if interesting_strings:
            result.append(f"Other strings: {', '.join(interesting_strings[:10])}")

        return "\n".join(result) if result else "No significant strings found"

    def _format_registry_data(self, report_data: Dict[str, Any]) -> str:
        """Format Windows registry access data"""
        registries = report_data.get("registries", {})
        if not registries:
            return "No registry access data (non-Windows or no registry operations)"

        registry_summary = []
        for key, accesses in registries.items():
            if isinstance(accesses, list) and accesses:
                registry_summary.append(f"{key}: {len(accesses)} accesses")

                # Add details for first access
                first_access = accesses[0]
                if isinstance(first_access, dict):
                    item = first_access.get("item", "N/A")
                    value = first_access.get("value", "N/A")
                    registry_summary.append(f"  - Item: {item}, Value: {value}")

        return "\n".join(registry_summary) if registry_summary else "No registry operations detected"

    def _format_symbols_data(self, report_data: Dict[str, Any]) -> str:
        """Format import/export symbols data"""
        import_symbols = report_data.get("import_symbols", {})
        export_symbols = report_data.get("export_symbols", {})

        result = []

        if import_symbols:
            imports = []
            for addr, symbol_info in list(import_symbols.items())[:10]:  # Limit to 10
                name = symbol_info.get("name", "Unknown")
                imports.append(f"{name} @ {addr}")
            result.append(f"Imported symbols: {', '.join(imports)}")

        if export_symbols:
            exports = []
            for addr, symbol_info in list(export_symbols.items())[:10]:  # Limit to 10
                name = symbol_info.get("name", "Unknown")
                exports.append(f"{name} @ {addr}")
            result.append(f"Exported symbols: {', '.join(exports)}")

        return "\n".join(result) if result else "No symbol information available"

    def _extract_network_indicators(self, report_data: Dict[str, Any]) -> str:
        """Extract network-related indicators"""
        indicators = []

        # Check strings for network indicators
        strings = report_data.get("strings", [])
        for string in strings:
            string = str(string).lower()
            if any(keyword in string for keyword in ['http', 'tcp', 'udp', 'socket', 'connect', 'bind']):
                indicators.append(string)

        # Check syscalls for network operations
        syscalls = report_data.get("syscalls", {}) or report_data.get("api", {})
        network_syscalls = []
        if syscalls:
            for syscall_name in syscalls.keys():
                if any(net_keyword in syscall_name.lower() for net_keyword in
                       ['socket', 'connect', 'bind', 'listen', 'accept', 'send', 'recv', 'http']):
                    network_syscalls.append(syscall_name)

        result = []
        if indicators:
            result.append(f"Network strings: {', '.join(indicators[:5])}")
        if network_syscalls:
            result.append(f"Network syscalls: {', '.join(network_syscalls)}")

        return "\n".join(result) if result else "No network indicators detected"

    def _format_system_interactions(self, report_data: Dict[str, Any]) -> str:
        """Format system interaction summary"""
        interactions = []

        # File operations
        syscalls = report_data.get("syscalls", {}) or report_data.get("api", {})
        if syscalls:
            file_ops = [name for name in syscalls.keys()
                       if any(keyword in name.lower() for keyword in ['file', 'read', 'write', 'open', 'close'])]
            if file_ops:
                interactions.append(f"File operations: {', '.join(file_ops[:5])}")

            # Process operations
            proc_ops = [name for name in syscalls.keys()
                       if any(keyword in name.lower() for keyword in ['process', 'thread', 'exec', 'fork'])]
            if proc_ops:
                interactions.append(f"Process operations: {', '.join(proc_ops[:5])}")

            # Memory operations
            mem_ops = [name for name in syscalls.keys()
                      if any(keyword in name.lower() for keyword in ['alloc', 'free', 'map', 'protect'])]
            if mem_ops:
                interactions.append(f"Memory operations: {', '.join(mem_ops[:5])}")

        return "\n".join(interactions) if interactions else "No significant system interactions detected"

    def _get_entry_point(self, report_data: Dict[str, Any]) -> str:
        """Get entry point information"""
        entry_point = report_data.get("entry_point")
        if entry_point:
            return f"0x{entry_point:x}" if isinstance(entry_point, int) else str(entry_point)
        return "Unknown"

    def analyze_multiple_reports(self, reports: List[Dict[str, Any]],
                               template_name: str = "comprehensive_analysis") -> List[Dict[str, Any]]:
        """Analyze multiple reports in batch

        Args:
            reports: List of report data dictionaries
            template_name: Analysis template to use

        Returns:
            List of analysis results
        """
        results = []
        for i, report in enumerate(reports):
            try:
                result = self.analyze_report(report, template_name)
                result["batch_info"] = {"index": i, "total": len(reports)}
                results.append(result)
            except Exception as e:
                error_result = {
                    "batch_info": {"index": i, "total": len(reports)},
                    "error": str(e),
                    "metadata": {"analysis_timestamp": datetime.now().isoformat()}
                }
                results.append(error_result)

        return results

    def save_analysis(self, analysis_result: Dict[str, Any], output_path: str) -> None:
        """Save analysis result to file

        Args:
            analysis_result: Analysis result from analyze_report()
            output_path: Path to save the analysis
        """
        if self.config.output_format == "json":
            with open(output_path, 'w') as f:
                json.dump(analysis_result, f, indent=2, default=str)

        elif self.config.output_format == "markdown":
            markdown_content = self._format_as_markdown(analysis_result)
            with open(output_path, 'w') as f:
                f.write(markdown_content)

        elif self.config.output_format == "html":
            html_content = self._format_as_html(analysis_result)
            with open(output_path, 'w') as f:
                f.write(html_content)

        else:
            raise ValueError(f"Unsupported output format: {self.config.output_format}")

    def _format_as_markdown(self, analysis_result: Dict[str, Any]) -> str:
        """Format analysis result as Markdown"""
        metadata = analysis_result.get("metadata", {})
        analysis = analysis_result.get("analysis", {})
        template_info = analysis.get("template_info", {})

        markdown = f"""# Qiling LLM Analysis Report

## Analysis Metadata
- **Timestamp**: {metadata.get('analysis_timestamp', 'Unknown')}
- **Template**: {template_info.get('name', 'Unknown')} ({template_info.get('type', 'Unknown')})
- **LLM Backend**: {metadata.get('llm_backend', 'Unknown')}
- **Model**: {metadata.get('llm_model', 'Unknown')}
- **Analysis Time**: {metadata.get('analysis_time_seconds', 'Unknown')} seconds

## Template Description
{template_info.get('description', 'No description available')}

## Analysis Results

{analysis.get('content', 'No analysis content available')}

---
*Generated by Qiling LLM Analyzer*
"""
        return markdown

    def _format_as_html(self, analysis_result: Dict[str, Any]) -> str:
        """Format analysis result as HTML"""
        metadata = analysis_result.get("metadata", {})
        analysis = analysis_result.get("analysis", {})
        template_info = analysis.get("template_info", {})

        # Convert markdown content to basic HTML formatting
        content = analysis.get('content', 'No analysis content available')
        content = content.replace('\n\n', '</p><p>')
        content = content.replace('\n', '<br>')
        content = f'<p>{content}</p>'

        html = f"""<!DOCTYPE html>
<html>
<head>
    <title>Qiling LLM Analysis Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .metadata {{ background-color: #f5f5f5; padding: 20px; border-radius: 5px; }}
        .analysis {{ margin-top: 30px; }}
        .footer {{ margin-top: 50px; font-style: italic; color: #666; }}
    </style>
</head>
<body>
    <h1>Qiling LLM Analysis Report</h1>

    <div class="metadata">
        <h2>Analysis Metadata</h2>
        <ul>
            <li><strong>Timestamp:</strong> {metadata.get('analysis_timestamp', 'Unknown')}</li>
            <li><strong>Template:</strong> {template_info.get('name', 'Unknown')} ({template_info.get('type', 'Unknown')})</li>
            <li><strong>LLM Backend:</strong> {metadata.get('llm_backend', 'Unknown')}</li>
            <li><strong>Model:</strong> {metadata.get('llm_model', 'Unknown')}</li>
            <li><strong>Analysis Time:</strong> {metadata.get('analysis_time_seconds', 'Unknown')} seconds</li>
        </ul>

        <h3>Template Description</h3>
        <p>{template_info.get('description', 'No description available')}</p>
    </div>

    <div class="analysis">
        <h2>Analysis Results</h2>
        {content}
    </div>

    <div class="footer">
        <p>Generated by Qiling LLM Analyzer</p>
    </div>
</body>
</html>"""
        return html

    def get_available_templates(self) -> List[str]:
        """Get list of available analysis templates"""
        return list_templates()

    def get_config_info(self) -> Dict[str, Any]:
        """Get current configuration information"""
        return {
            "config": self.config.to_dict(),
            "llm_ready": self.is_ready(),
            "available_templates": self.get_available_templates()
        }
