#!/usr/bin/env python3
# 
# Cross Platform and Multi Architecture Advanced Binary Emulation Framework
# Report Enhancement System
#

import json
from typing import Dict, Any, List, Optional
from datetime import datetime

from .analyzer import QlLLMAnalyzer
from .config import LLMConfig
from ..report import generate_report


class QlReportEnhancer:
    """System to enhance existing Qiling reports with LLM-generated insights"""
    
    def __init__(self, config: Optional[LLMConfig] = None):
        """Initialize the report enhancer
        
        Args:
            config: LLM configuration. If None, uses default config.
        """
        self.analyzer = QlLLMAnalyzer(config)
        self.config = self.analyzer.config
    
    def enhance_report(self, report_data: Dict[str, Any], 
                      enhancement_types: List[str] = None) -> Dict[str, Any]:
        """Enhance a Qiling report with LLM analysis
        
        Args:
            report_data: Original report data from generate_report()
            enhancement_types: List of enhancement types to apply
                             Options: ['security', 'behavior', 'summary', 'recommendations']
            
        Returns:
            Enhanced report with LLM insights
        """
        if enhancement_types is None:
            enhancement_types = ['security', 'behavior', 'summary', 'recommendations']
        
        enhanced_report = report_data.copy()
        enhanced_report['llm_enhancements'] = {
            'timestamp': datetime.now().isoformat(),
            'enhancement_types': enhancement_types,
            'llm_config': {
                'backend': self.config.backend,
                'model': self.config.model
            }
        }
        
        # Apply each enhancement type
        for enhancement_type in enhancement_types:
            try:
                enhancement = self._generate_enhancement(report_data, enhancement_type)
                enhanced_report['llm_enhancements'][enhancement_type] = enhancement
            except Exception as e:
                enhanced_report['llm_enhancements'][enhancement_type] = {
                    'error': str(e),
                    'status': 'failed'
                }
        
        return enhanced_report
    
    def _generate_enhancement(self, report_data: Dict[str, Any], enhancement_type: str) -> Dict[str, Any]:
        """Generate specific type of enhancement"""
        
        enhancement_prompts = {
            'security': self._create_security_prompt,
            'behavior': self._create_behavior_prompt,
            'summary': self._create_summary_prompt,
            'recommendations': self._create_recommendations_prompt
        }
        
        if enhancement_type not in enhancement_prompts:
            raise ValueError(f"Unknown enhancement type: {enhancement_type}")
        
        # Create prompt for this enhancement type
        prompt_func = enhancement_prompts[enhancement_type]
        system_prompt, user_prompt = prompt_func(report_data)
        
        # Generate enhancement using LLM
        enhancement_text = self.analyzer.llm_interface.generate(
            prompt=user_prompt,
            system_prompt=system_prompt
        )
        
        return {
            'content': enhancement_text,
            'type': enhancement_type,
            'status': 'success'
        }
    
    def _create_security_prompt(self, report_data: Dict[str, Any]) -> tuple:
        """Create security assessment prompt"""
        system_prompt = """You are a cybersecurity expert. Analyze the provided execution data and provide a concise security assessment focusing on potential threats, vulnerabilities, and security implications. Be specific and actionable."""
        
        processed_data = self.analyzer._process_report_data(report_data)
        
        user_prompt = f"""
Provide a security assessment for this binary execution:

File: {processed_data['filename']}
Platform: {processed_data['arch']} / {processed_data['os']}

System Calls: {processed_data['syscalls_summary'][:500]}...
Strings: {processed_data['strings_summary'][:300]}...
Network: {processed_data['network_indicators']}

Focus on:
1. Threat level (Low/Medium/High/Critical)
2. Key security concerns
3. Potential attack vectors
4. Immediate risks

Keep response under 200 words.
"""
        
        return system_prompt, user_prompt
    
    def _create_behavior_prompt(self, report_data: Dict[str, Any]) -> tuple:
        """Create behavior analysis prompt"""
        system_prompt = """You are a program behavior analyst. Analyze the execution data and explain what this program does in simple, clear terms. Focus on the main functionality and purpose."""
        
        processed_data = self.analyzer._process_report_data(report_data)
        
        user_prompt = f"""
Explain what this program does based on its execution:

File: {processed_data['filename']}
Platform: {processed_data['arch']} / {processed_data['os']}

System Calls: {processed_data['syscalls_summary'][:500]}...
System Interactions: {processed_data['system_interactions']}

Provide:
1. Primary function/purpose
2. Key operations performed
3. Resources accessed

Keep response under 150 words.
"""
        
        return system_prompt, user_prompt
    
    def _create_summary_prompt(self, report_data: Dict[str, Any]) -> tuple:
        """Create executive summary prompt"""
        system_prompt = """You are a technical analyst creating executive summaries. Provide a concise, high-level summary of the binary analysis that would be useful for management or non-technical stakeholders."""
        
        processed_data = self.analyzer._process_report_data(report_data)
        
        user_prompt = f"""
Create an executive summary for this binary analysis:

File: {processed_data['filename']}
Platform: {processed_data['arch']} / {processed_data['os']}

Key Data Points:
- System calls: {len(str(processed_data['syscalls_summary']).split('\\n'))} different operations
- Strings extracted: {len(str(processed_data['strings_summary']).split(','))} items
- Network activity: {processed_data['network_indicators']}

Provide:
1. What was analyzed
2. Key findings
3. Overall assessment
4. Business impact (if any)

Keep response under 100 words.
"""
        
        return system_prompt, user_prompt
    
    def _create_recommendations_prompt(self, report_data: Dict[str, Any]) -> tuple:
        """Create recommendations prompt"""
        system_prompt = """You are a security consultant providing actionable recommendations. Based on the analysis, suggest specific next steps, additional analysis, or security measures."""
        
        processed_data = self.analyzer._process_report_data(report_data)
        
        user_prompt = f"""
Provide recommendations based on this analysis:

File: {processed_data['filename']}
Platform: {processed_data['arch']} / {processed_data['os']}

Analysis Summary:
- System operations: {processed_data['syscalls_summary'][:300]}...
- Network indicators: {processed_data['network_indicators']}
- System changes: {processed_data['registry_summary'][:200]}...

Recommend:
1. Additional analysis steps
2. Security measures
3. Monitoring recommendations
4. Investigation priorities

Keep response under 150 words.
"""
        
        return system_prompt, user_prompt
    
    def enhance_qiling_instance(self, ql, enhancement_types: List[str] = None) -> Dict[str, Any]:
        """Enhance report directly from Qiling instance
        
        Args:
            ql: Qiling instance after execution
            enhancement_types: List of enhancement types to apply
            
        Returns:
            Enhanced report dictionary
        """
        # Generate base report
        report_data = generate_report(ql)
        
        # Enhance the report
        return self.enhance_report(report_data, enhancement_types)
    
    def create_enhanced_summary(self, enhanced_report: Dict[str, Any]) -> str:
        """Create a formatted summary of all enhancements
        
        Args:
            enhanced_report: Enhanced report from enhance_report()
            
        Returns:
            Formatted summary string
        """
        enhancements = enhanced_report.get('llm_enhancements', {})
        
        summary = f"""# Enhanced Analysis Summary

**File**: {enhanced_report.get('filename', 'Unknown')}
**Platform**: {enhanced_report.get('arch', 'Unknown')} / {enhanced_report.get('os', 'Unknown')}
**Analysis Time**: {enhancements.get('timestamp', 'Unknown')}
**LLM Model**: {enhancements.get('llm_config', {}).get('model', 'Unknown')}

"""
        
        # Add each enhancement section
        for enhancement_type in ['summary', 'behavior', 'security', 'recommendations']:
            enhancement_data = enhancements.get(enhancement_type, {})
            
            if enhancement_data.get('status') == 'success':
                content = enhancement_data.get('content', 'No content available')
                summary += f"""## {enhancement_type.title()} Analysis

{content}

"""
            elif enhancement_data.get('status') == 'failed':
                summary += f"""## {enhancement_type.title()} Analysis

*Analysis failed: {enhancement_data.get('error', 'Unknown error')}*

"""
        
        summary += "---\n*Generated by Qiling LLM Report Enhancer*"
        
        return summary
