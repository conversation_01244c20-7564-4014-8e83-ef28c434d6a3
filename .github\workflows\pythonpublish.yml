name: PyPI 📦 Distribution

on: [push]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "^3.8"
      - name: Install dependencies
        run: pipx install poetry
      - name: Build distribution 📦
        run: poetry build -n
      - uses: actions/upload-artifact@v4
        with:
          path: ${{ github.workspace }}/dist/*

  publish:
    needs: [build]
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags')
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: artifact
          path: dist

      - name: Publish distribution 📦 to test PyPI
        uses: pypa/gh-action-pypi-publish@master
        with:
          user: __token__
          password: ${{ secrets.testpypi_pass }}
          repository_url: https://test.pypi.org/legacy/

      - name: Publish distribution 📦 to PyPI
        if: ${{ success() }}
        uses: pypa/gh-action-pypi-publish@master
        with:
          user: __token__
          password: ${{ secrets.pypi_pass }}
