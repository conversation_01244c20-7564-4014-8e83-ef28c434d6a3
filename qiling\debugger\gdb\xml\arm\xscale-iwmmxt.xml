<?xml version="1.0"?>
<!-- Copyright (C) 2007-2020 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->

<!DOCTYPE feature SYSTEM "gdb-target.dtd">
<feature name="org.gnu.gdb.xscale.iwmmxt">
  <vector id="iwmmxt_v8u8" type="uint8" count="8"/>
  <vector id="iwmmxt_v4u16" type="uint16" count="4"/>
  <vector id="iwmmxt_v2u32" type="uint32" count="2"/>
  <union id="iwmmxt_vec64i">
    <field name="u8" type="iwmmxt_v8u8"/>
    <field name="u16" type="iwmmxt_v4u16"/>
    <field name="u32" type="iwmmxt_v2u32"/>
    <field name="u64" type="uint64"/>
  </union>

  <reg name="wR0" bitsize="64" type="iwmmxt_vec64i"/>
  <reg name="wR1" bitsize="64" type="iwmmxt_vec64i"/>
  <reg name="wR2" bitsize="64" type="iwmmxt_vec64i"/>
  <reg name="wR3" bitsize="64" type="iwmmxt_vec64i"/>
  <reg name="wR4" bitsize="64" type="iwmmxt_vec64i"/>
  <reg name="wR5" bitsize="64" type="iwmmxt_vec64i"/>
  <reg name="wR6" bitsize="64" type="iwmmxt_vec64i"/>
  <reg name="wR7" bitsize="64" type="iwmmxt_vec64i"/>
  <reg name="wR8" bitsize="64" type="iwmmxt_vec64i"/>
  <reg name="wR9" bitsize="64" type="iwmmxt_vec64i"/>
  <reg name="wR10" bitsize="64" type="iwmmxt_vec64i"/>
  <reg name="wR11" bitsize="64" type="iwmmxt_vec64i"/>
  <reg name="wR12" bitsize="64" type="iwmmxt_vec64i"/>
  <reg name="wR13" bitsize="64" type="iwmmxt_vec64i"/>
  <reg name="wR14" bitsize="64" type="iwmmxt_vec64i"/>
  <reg name="wR15" bitsize="64" type="iwmmxt_vec64i"/>

  <reg name="wCSSF" bitsize="32" type="int" group="vector"/>
  <reg name="wCASF" bitsize="32" type="int" group="vector"/>

  <reg name="wCGR0" bitsize="32" type="int" group="vector"/>
  <reg name="wCGR1" bitsize="32" type="int" group="vector"/>
  <reg name="wCGR2" bitsize="32" type="int" group="vector"/>
  <reg name="wCGR3" bitsize="32" type="int" group="vector"/>
</feature>
