#!/usr/bin/env python3
#
# Cross Platform and Multi Architecture Advanced Binary Emulation Framework
#

from enum import Enum
from typing import Union


class X86_CPU_MODEL(Enum):

    # generic
    GENERIC_QEMU64 = 0
    GENERIC_KVM64 = 3
    GENERIC_QEMU32 = 4
    GENERIC_KVM32 = 5

    # amd
    AMD_PHENOM = 1
    AMD_ATHLON = 11
    AMD_OPTERON_G1 = 30
    AMD_OPTERON_G2 = 31
    AMD_OPTERON_G3 = 32
    AMD_OPTERON_G4 = 33
    AMD_OPTERON_G5 = 34
    AMD_EPYC = 35
    AMD_DHYANA = 36
    AMD_EPYC_ROME = 37

    # intel
    INTEL_CORE2DUO = 2
    INTEL_COREDUO = 6
    INTEL_486 = 7
    INTEL_PENTIUM = 8
    INTEL_PENTIUM2 = 9
    INTEL_PENTIUM3 = 10
    INTEL_N270 = 12
    INTEL_CONROE = 13
    INTEL_PENRYN = 14
    INTEL_NEHALEM = 15
    INTEL_WESTMERE = 16
    INTEL_SANDYBRIDGE = 17
    INTEL_IVYBRIDGE = 18
    INTEL_HASWELL = 19
    INTEL_BROADWELL = 20
    INTEL_SKYLAKE_CLIENT = 21
    INTEL_SKYLAKE_SERVER = 22
    INTEL_CASCADELAKE_SERVER = 23
    INTEL_COOPERLAKE = 24
    INTEL_ICELAKE_CLIENT = 25
    INTEL_ICELAKE_SERVER = 26
    INTEL_DENVERTON = 27
    INTEL_SNOWRIDGE = 28
    INTEL_KNIGHTSMILL = 29


class ARM_CPU_MODEL(Enum):
    ARM_926 = 0
    ARM_946 = 1
    ARM_1026 = 2
    ARM_1136_R2 = 3
    ARM_1136 = 4
    ARM_1176 = 5
    ARM_11MPCORE = 6
    ARM_CORTEX_M0 = 7
    ARM_CORTEX_M3 = 8
    ARM_CORTEX_M4 = 9
    ARM_CORTEX_M7 = 10
    ARM_CORTEX_M33 = 11
    ARM_CORTEX_R5 = 12
    ARM_CORTEX_R5F = 13
    ARM_CORTEX_A7 = 14
    ARM_CORTEX_A8 = 15
    ARM_CORTEX_A9 = 16
    ARM_CORTEX_A15 = 17
    ARM_TI925T = 18
    ARM_SA1100 = 19
    ARM_SA1110 = 20
    ARM_PXA250 = 21
    ARM_PXA255 = 22
    ARM_PXA260 = 23
    ARM_PXA261 = 24
    ARM_PXA262 = 25
    ARM_PXA270 = 26
    ARM_PXA270A0 = 27
    ARM_PXA270A1 = 28
    ARM_PXA270B0 = 29
    ARM_PXA270B1 = 30
    ARM_PXA270C0 = 31
    ARM_PXA270C5 = 32
    ARM_MAX = 33


class ARM64_CPU_MODEL(Enum):
    ARM64_A57 = 0
    ARM64_A53 = 1
    ARM64_A72 = 2
    ARM64_MAX = 3


class MIPS_CPU_MODEL(Enum):
    MIPS32_4KC = 0
    MIPS32_4KM = 1
    MIPS32_4KECR1 = 2
    MIPS32_4KEMR1 = 3
    MIPS32_4KEC = 4
    MIPS32_4KEM = 5
    MIPS32_24KC = 6
    MIPS32_24KEC = 7
    MIPS32_24KF = 8
    MIPS32_34KF = 9
    MIPS32_74KF = 10
    MIPS32_M14K = 11
    MIPS32_M14KC = 12
    MIPS32_P5600 = 13
    MIPS32_MIPS32R6_GENERIC = 14
    MIPS32_I7200 = 15


class PPC_CPU_MODEL(Enum):
    PPC32_401 = 0
    PPC32_401A1 = 1
    PPC32_401B2 = 2
    PPC32_401C2 = 3
    PPC32_401D2 = 4
    PPC32_401E2 = 5
    PPC32_401F2 = 6
    PPC32_401G2 = 7
    PPC32_IOP480 = 8
    PPC32_COBRA = 9
    PPC32_403GA = 10
    PPC32_403GB = 11
    PPC32_403GC = 12
    PPC32_403GCX = 13
    PPC32_405D2 = 14
    PPC32_405D4 = 15
    PPC32_405CRA = 16
    PPC32_405CRB = 17
    PPC32_405CRC = 18
    PPC32_405EP = 19
    PPC32_405EZ = 20
    PPC32_405GPA = 21
    PPC32_405GPB = 22
    PPC32_405GPC = 23
    PPC32_405GPD = 24
    PPC32_405GPR = 25
    PPC32_405LP = 26
    PPC32_NPE405H = 27
    PPC32_NPE405H2 = 28
    PPC32_NPE405L = 29
    PPC32_NPE4GS3 = 30
    PPC32_STB03 = 31
    PPC32_STB04 = 32
    PPC32_STB25 = 33
    PPC32_X2VP4 = 34
    PPC32_X2VP20 = 35
    PPC32_440_XILINX = 36
    PPC32_440_XILINX_W_DFPU = 37
    PPC32_440EPA = 38
    PPC32_440EPB = 39
    PPC32_440EPX = 40
    PPC32_460EXB = 41
    PPC32_G2 = 42
    PPC32_G2H4 = 43
    PPC32_G2GP = 44
    PPC32_G2LS = 45
    PPC32_G2HIP3 = 46
    PPC32_G2HIP4 = 47
    PPC32_MPC603 = 48
    PPC32_G2LE = 49
    PPC32_G2LEGP = 50
    PPC32_G2LELS = 51
    PPC32_G2LEGP1 = 52
    PPC32_G2LEGP3 = 53
    PPC32_MPC5200_V10 = 54
    PPC32_MPC5200_V11 = 55
    PPC32_MPC5200_V12 = 56
    PPC32_MPC5200B_V20 = 57
    PPC32_MPC5200B_V21 = 58
    PPC32_E200Z5 = 59
    PPC32_E200Z6 = 60
    PPC32_E300C1 = 61
    PPC32_E300C2 = 62
    PPC32_E300C3 = 63
    PPC32_E300C4 = 64
    PPC32_MPC8343 = 65
    PPC32_MPC8343A = 66
    PPC32_MPC8343E = 67
    PPC32_MPC8343EA = 68
    PPC32_MPC8347T = 69
    PPC32_MPC8347P = 70
    PPC32_MPC8347AT = 71
    PPC32_MPC8347AP = 72
    PPC32_MPC8347ET = 73
    PPC32_MPC8347EP = 74
    PPC32_MPC8347EAT = 75
    PPC32_MPC8347EAP = 76
    PPC32_MPC8349 = 77
    PPC32_MPC8349A = 78
    PPC32_MPC8349E = 79
    PPC32_MPC8349EA = 80
    PPC32_MPC8377 = 81
    PPC32_MPC8377E = 82
    PPC32_MPC8378 = 83
    PPC32_MPC8378E = 84
    PPC32_MPC8379 = 85
    PPC32_MPC8379E = 86
    PPC32_E500_V10 = 87
    PPC32_E500_V20 = 88
    PPC32_E500V2_V10 = 89
    PPC32_E500V2_V20 = 90
    PPC32_E500V2_V21 = 91
    PPC32_E500V2_V22 = 92
    PPC32_E500V2_V30 = 93
    PPC32_E500MC = 94
    PPC32_MPC8533_V10 = 95
    PPC32_MPC8533_V11 = 96
    PPC32_MPC8533E_V10 = 97
    PPC32_MPC8533E_V11 = 98
    PPC32_MPC8540_V10 = 99
    PPC32_MPC8540_V20 = 100
    PPC32_MPC8540_V21 = 101
    PPC32_MPC8541_V10 = 102
    PPC32_MPC8541_V11 = 103
    PPC32_MPC8541E_V10 = 104
    PPC32_MPC8541E_V11 = 105
    PPC32_MPC8543_V10 = 106
    PPC32_MPC8543_V11 = 107
    PPC32_MPC8543_V20 = 108
    PPC32_MPC8543_V21 = 109
    PPC32_MPC8543E_V10 = 110
    PPC32_MPC8543E_V11 = 111
    PPC32_MPC8543E_V20 = 112
    PPC32_MPC8543E_V21 = 113
    PPC32_MPC8544_V10 = 114
    PPC32_MPC8544_V11 = 115
    PPC32_MPC8544E_V10 = 116
    PPC32_MPC8544E_V11 = 117
    PPC32_MPC8545_V20 = 118
    PPC32_MPC8545_V21 = 119
    PPC32_MPC8545E_V20 = 120
    PPC32_MPC8545E_V21 = 121
    PPC32_MPC8547E_V20 = 122
    PPC32_MPC8547E_V21 = 123
    PPC32_MPC8548_V10 = 124
    PPC32_MPC8548_V11 = 125
    PPC32_MPC8548_V20 = 126
    PPC32_MPC8548_V21 = 127
    PPC32_MPC8548E_V10 = 128
    PPC32_MPC8548E_V11 = 129
    PPC32_MPC8548E_V20 = 130
    PPC32_MPC8548E_V21 = 131
    PPC32_MPC8555_V10 = 132
    PPC32_MPC8555_V11 = 133
    PPC32_MPC8555E_V10 = 134
    PPC32_MPC8555E_V11 = 135
    PPC32_MPC8560_V10 = 136
    PPC32_MPC8560_V20 = 137
    PPC32_MPC8560_V21 = 138
    PPC32_MPC8567 = 139
    PPC32_MPC8567E = 140
    PPC32_MPC8568 = 141
    PPC32_MPC8568E = 142
    PPC32_MPC8572 = 143
    PPC32_MPC8572E = 144
    PPC32_E600 = 145
    PPC32_MPC8610 = 146
    PPC32_MPC8641 = 147
    PPC32_MPC8641D = 148
    PPC32_601_V0 = 149
    PPC32_601_V1 = 150
    PPC32_601_V2 = 151
    PPC32_602 = 152
    PPC32_603 = 153
    PPC32_603E_V1_1 = 154
    PPC32_603E_V1_2 = 155
    PPC32_603E_V1_3 = 156
    PPC32_603E_V1_4 = 157
    PPC32_603E_V2_2 = 158
    PPC32_603E_V3 = 159
    PPC32_603E_V4 = 160
    PPC32_603E_V4_1 = 161
    PPC32_603E7 = 162
    PPC32_603E7T = 163
    PPC32_603E7V = 164
    PPC32_603E7V1 = 165
    PPC32_603E7V2 = 166
    PPC32_603P = 167
    PPC32_604 = 168
    PPC32_604E_V1_0 = 169
    PPC32_604E_V2_2 = 170
    PPC32_604E_V2_4 = 171
    PPC32_604R = 172
    PPC32_740_V1_0 = 173
    PPC32_750_V1_0 = 174
    PPC32_740_V2_0 = 175
    PPC32_750_V2_0 = 176
    PPC32_740_V2_1 = 177
    PPC32_750_V2_1 = 178
    PPC32_740_V2_2 = 179
    PPC32_750_V2_2 = 180
    PPC32_740_V3_0 = 181
    PPC32_750_V3_0 = 182
    PPC32_740_V3_1 = 183
    PPC32_750_V3_1 = 184
    PPC32_740E = 185
    PPC32_750E = 186
    PPC32_740P = 187
    PPC32_750P = 188
    PPC32_750CL_V1_0 = 189
    PPC32_750CL_V2_0 = 190
    PPC32_750CX_V1_0 = 191
    PPC32_750CX_V2_0 = 192
    PPC32_750CX_V2_1 = 193
    PPC32_750CX_V2_2 = 194
    PPC32_750CXE_V2_1 = 195
    PPC32_750CXE_V2_2 = 196
    PPC32_750CXE_V2_3 = 197
    PPC32_750CXE_V2_4 = 198
    PPC32_750CXE_V2_4B = 199
    PPC32_750CXE_V3_0 = 200
    PPC32_750CXE_V3_1 = 201
    PPC32_750CXE_V3_1B = 202
    PPC32_750CXR = 203
    PPC32_750FL = 204
    PPC32_750FX_V1_0 = 205
    PPC32_750FX_V2_0 = 206
    PPC32_750FX_V2_1 = 207
    PPC32_750FX_V2_2 = 208
    PPC32_750FX_V2_3 = 209
    PPC32_750GL = 210
    PPC32_750GX_V1_0 = 211
    PPC32_750GX_V1_1 = 212
    PPC32_750GX_V1_2 = 213
    PPC32_750L_V2_0 = 214
    PPC32_750L_V2_1 = 215
    PPC32_750L_V2_2 = 216
    PPC32_750L_V3_0 = 217
    PPC32_750L_V3_2 = 218
    PPC32_745_V1_0 = 219
    PPC32_755_V1_0 = 220
    PPC32_745_V1_1 = 221
    PPC32_755_V1_1 = 222
    PPC32_745_V2_0 = 223
    PPC32_755_V2_0 = 224
    PPC32_745_V2_1 = 225
    PPC32_755_V2_1 = 226
    PPC32_745_V2_2 = 227
    PPC32_755_V2_2 = 228
    PPC32_745_V2_3 = 229
    PPC32_755_V2_3 = 230
    PPC32_745_V2_4 = 231
    PPC32_755_V2_4 = 232
    PPC32_745_V2_5 = 233
    PPC32_755_V2_5 = 234
    PPC32_745_V2_6 = 235
    PPC32_755_V2_6 = 236
    PPC32_745_V2_7 = 237
    PPC32_755_V2_7 = 238
    PPC32_745_V2_8 = 239
    PPC32_755_V2_8 = 240
    PPC32_7400_V1_0 = 241
    PPC32_7400_V1_1 = 242
    PPC32_7400_V2_0 = 243
    PPC32_7400_V2_1 = 244
    PPC32_7400_V2_2 = 245
    PPC32_7400_V2_6 = 246
    PPC32_7400_V2_7 = 247
    PPC32_7400_V2_8 = 248
    PPC32_7400_V2_9 = 249
    PPC32_7410_V1_0 = 250
    PPC32_7410_V1_1 = 251
    PPC32_7410_V1_2 = 252
    PPC32_7410_V1_3 = 253
    PPC32_7410_V1_4 = 254
    PPC32_7448_V1_0 = 255
    PPC32_7448_V1_1 = 256
    PPC32_7448_V2_0 = 257
    PPC32_7448_V2_1 = 258
    PPC32_7450_V1_0 = 259
    PPC32_7450_V1_1 = 260
    PPC32_7450_V1_2 = 261
    PPC32_7450_V2_0 = 262
    PPC32_7450_V2_1 = 263
    PPC32_7441_V2_1 = 264
    PPC32_7441_V2_3 = 265
    PPC32_7451_V2_3 = 266
    PPC32_7441_V2_10 = 267
    PPC32_7451_V2_10 = 268
    PPC32_7445_V1_0 = 269
    PPC32_7455_V1_0 = 270
    PPC32_7445_V2_1 = 271
    PPC32_7455_V2_1 = 272
    PPC32_7445_V3_2 = 273
    PPC32_7455_V3_2 = 274
    PPC32_7445_V3_3 = 275
    PPC32_7455_V3_3 = 276
    PPC32_7445_V3_4 = 277
    PPC32_7455_V3_4 = 278
    PPC32_7447_V1_0 = 279
    PPC32_7457_V1_0 = 280
    PPC32_7447_V1_1 = 281
    PPC32_7457_V1_1 = 282
    PPC32_7457_V1_2 = 283
    PPC32_7447A_V1_0 = 284
    PPC32_7457A_V1_0 = 285
    PPC32_7447A_V1_1 = 286
    PPC32_7457A_V1_1 = 287
    PPC32_7447A_V1_2 = 288
    PPC32_7457A_V1_2 = 289


class RISCV_CPU_MODEL(Enum):
    RISCV32_ANY = 0
    RISCV32_BASE32 = 1
    RISCV32_SIFIVE_E31 = 2
    RISCV32_SIFIVE_U34 = 3


class RISCV64_CPU_MODEL(Enum):
    RISCV64_ANY = 0
    RISCV64_BASE64 = 1
    RISCV64_SIFIVE_E51 = 2
    RISCV64_SIFIVE_U54 = 3


QL_CPU = Union[
    X86_CPU_MODEL,
    ARM_CPU_MODEL,
    ARM64_CPU_MODEL,
    MIPS_CPU_MODEL,
    PPC_CPU_MODEL,
    RISCV_CPU_MODEL,
    RISCV64_CPU_MODEL
]


__all__ = [
    'X86_CPU_MODEL',
    'ARM_CPU_MODEL',
    'ARM64_CPU_MODEL',
    'MIPS_CPU_MODEL',
    'PPC_CPU_MODEL',
    'RISCV_CPU_MODEL',
    'RISCV64_CPU_MODEL',
    'QL_CPU'
]
