#!/usr/bin/env python3
"""
Basic example of using Qiling LLM Analyzer

This example shows how to:
1. Set up LLM configuration
2. Run binary analysis with Qiling
3. Generate LLM-powered analysis report
4. Save results in different formats
"""

import sys
import os
from pathlib import Path

# Add qiling to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from qiling import Qiling
from qiling.extensions.llm_analyzer import Ql<PERSON><PERSON>nal<PERSON><PERSON>, LLMConfig
from qiling.extensions.llm_analyzer.config_manager import ConfigManager


def main():
    """Main example function"""
    
    # Example binary path (replace with your binary)
    binary_path = "examples/rootfs/x8664_windows/bin/x8664_hello.exe"
    rootfs_path = "examples/rootfs/x8664_windows"
    
    if not os.path.exists(binary_path):
        print(f"Binary not found: {binary_path}")
        print("Please provide a valid binary path")
        return
    
    print("=== Qiling LLM Analysis Example ===\n")
    
    # Step 1: Set up configuration
    print("1. Setting up LLM configuration...")
    
    # Option A: Use default configuration
    config = LLMConfig(
        backend="ollama",
        model="llama3.2:3b",
        base_url="http://localhost:11434",
        temperature=0.1,
        max_tokens=4096
    )
    
    # Option B: Use configuration manager with templates
    # config_manager = ConfigManager()
    # config = config_manager.create_config("my_analysis", "ollama_local")
    
    print(f"   Backend: {config.backend}")
    print(f"   Model: {config.model}")
    print(f"   Base URL: {config.base_url}")
    
    # Step 2: Initialize LLM analyzer
    print("\n2. Initializing LLM analyzer...")
    analyzer = QlLLMAnalyzer(config)
    
    if not analyzer.is_ready():
        print("   ERROR: LLM analyzer is not ready!")
        print("   Please check that your LLM backend is running and accessible")
        print(f"   Backend: {config.backend} at {config.base_url}")
        return
    
    print("   LLM analyzer ready!")
    
    # Step 3: Run Qiling emulation
    print("\n3. Running Qiling emulation...")
    
    try:
        ql = Qiling([binary_path], rootfs_path, verbose=False)
        print(f"   Binary: {binary_path}")
        print(f"   Architecture: {ql.arch.type}")
        print(f"   OS: {ql.os.type}")
        
        # Run the binary
        ql.run()
        print("   Emulation completed successfully")
        
    except Exception as e:
        print(f"   ERROR: Emulation failed: {e}")
        return
    
    # Step 4: Generate LLM analysis
    print("\n4. Generating LLM analysis...")
    
    try:
        # Analyze with different templates
        templates_to_try = [
            "comprehensive_analysis",
            "security_assessment", 
            "behavior_analysis"
        ]
        
        results = {}
        
        for template in templates_to_try:
            print(f"   Analyzing with template: {template}")
            
            try:
                result = analyzer.analyze_qiling_instance(ql, template)
                results[template] = result
                
                analysis_time = result['metadata']['analysis_time_seconds']
                print(f"   ✓ Completed in {analysis_time} seconds")
                
            except Exception as e:
                print(f"   ✗ Failed: {e}")
                continue
        
        if not results:
            print("   ERROR: All analysis attempts failed")
            return
            
    except Exception as e:
        print(f"   ERROR: Analysis failed: {e}")
        return
    
    # Step 5: Save results
    print("\n5. Saving analysis results...")
    
    output_dir = Path("analysis_output")
    output_dir.mkdir(exist_ok=True)
    
    for template, result in results.items():
        # Save as JSON
        json_path = output_dir / f"{template}_analysis.json"
        analyzer.save_analysis(result, str(json_path))
        print(f"   Saved JSON: {json_path}")
        
        # Save as Markdown
        md_path = output_dir / f"{template}_analysis.md"
        analyzer.config.output_format = "markdown"
        analyzer.save_analysis(result, str(md_path))
        print(f"   Saved Markdown: {md_path}")
        
        # Print summary to console
        print(f"\n   === {template.upper()} SUMMARY ===")
        analysis_content = result['analysis']['content']
        # Print first 300 characters
        summary = analysis_content[:300] + "..." if len(analysis_content) > 300 else analysis_content
        print(f"   {summary}")
    
    print(f"\n=== Analysis Complete ===")
    print(f"Results saved to: {output_dir.absolute()}")
    print(f"Templates analyzed: {list(results.keys())}")


def check_requirements():
    """Check if requirements are met"""
    print("Checking requirements...")
    
    # Check if LLM backend is accessible
    import requests
    
    backends_to_check = [
        ("Ollama", "http://localhost:11434/api/tags"),
        ("llama.cpp", "http://localhost:8080/health"),
        ("LM Studio", "http://localhost:1234/v1/models")
    ]
    
    available_backends = []
    
    for name, url in backends_to_check:
        try:
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                available_backends.append(name)
                print(f"✓ {name} is available")
            else:
                print(f"✗ {name} returned status {response.status_code}")
        except requests.exceptions.RequestException:
            print(f"✗ {name} is not accessible")
    
    if not available_backends:
        print("\nWARNING: No LLM backends are accessible!")
        print("Please start one of the following:")
        print("- Ollama: ollama serve")
        print("- llama.cpp: ./server -m model.gguf")
        print("- LM Studio: Start LM Studio and enable local server")
        return False
    
    print(f"\nAvailable backends: {', '.join(available_backends)}")
    return True


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--check":
        check_requirements()
    else:
        main()
