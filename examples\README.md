Qiling - Sample Codes
---


```
cd examples
```

#### Shellcode Executor

- Multi ARCH, Cross Platform Shellcode Executor

```
$ python3 shellcode_run.py
```

---
### X86_64 FreeBSD binaries

- X86_64 FreeBSD binary, simple helloworld

```
$ python3 hello_x8664_freebsd.py
```

---
### X86_64 macos binaries

- X86_64 macos binary, simple helloworld

```
$ python3 hello_x8664_macos.py
```

---
### X86 macos binaries

- X86 macos binary, simple helloworld

```
$ python3 hello_x86_macos.py
```

---
### X86_64 Linux binaries

- X86_64 Linux binary, helloworld with manual disassembler output

```
$ python3 hello_x8664_linux_disasm.py
```

- X86_64 Linux binary, simple helloworld

```
$ python3 hello_x8664_linux.py
```

---
### X86 Linux binaries

- X86 Linux binary, reversing.kr linux crackme bruteforce solver

```
$ python3 crackme_x86_linux.py
```

- X86 Linux binary, simple helloworld

```
$ python3 hello_x86_linux.py
```

---
### X86 Windows binaries

- X86 Windows binary, ctf challage from BJWXB2019, bruteforce solver

```
$ python3 crackme_x86_windows.py
```

- X86 Windows binary, example, how to use ql.patch and ql.set_callback

```
$ python3 crackme_x86_windows_setcallback.py
```

- X86 Windows binary, example, ql.set_callback

```
$ python3 crackme_x86_windows_unpatch.py
```

- X86 Windows binary, helloworld with debug mode on

```
$ python3 hello_x86_windows_debug.py
```

- X86 Windows binary, with multithreading

```
$ python3 multithreading_x86_windows.py
```

- X86 Windows binary, catching wannacry's killerswtich

```
$ python3 wannacry_x86_windows_setexit.py
```

---
### X86_64 Windows binaries

- X86_64 Windows binary, example on how to set output

```
$ python3 disasm_x8664_windows.py
```

---
### ARM64 Linux binaries

- ARM64 Linux binary, exit when there is a write to memory 0x555555566260

```
$ python3 exitmemwrite_arm64_linux.py
```

- ARM64 Linux binary, simple helloworld

```
$ python3 hello_arm64_linux.py
```

---
#### ARM binaries

- ARM Linux binary, simple helloworld

```
$ python3 hello_arm_linux.py
```

- ARM Linux binary, simple stack overflow

```
$ python3 bofsample_arm_linux.py
```

- ARM Linux binary, simple helloworld with debug mode

```
$ python3 hello_arm_linux_debug.py
```

---
#### MIPS32EL binaries

- MIPS32EL Linux binary, simple helloworld

```
$ python3 hello_mips32el_linux.py
```
