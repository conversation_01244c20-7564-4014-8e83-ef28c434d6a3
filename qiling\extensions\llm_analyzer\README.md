# Qiling LLM Analyzer

An intelligent analysis system that integrates local Large Language Models (LLMs) with the Qiling emulation framework to automatically generate comprehensive reports and insights from binary analysis.

## Features

- **Local LLM Integration**: Support for Ollama, llama.cpp, and OpenAI-compatible APIs
- **Multiple Analysis Templates**: Security assessment, malware analysis, behavior analysis, and more
- **Report Enhancement**: Enhance existing Qiling reports with AI-generated insights
- **Flexible Configuration**: Easy configuration management for different LLM backends
- **Batch Processing**: Analyze multiple binaries efficiently
- **Multiple Output Formats**: JSON, Markdown, and HTML output support

## Quick Start

### 1. Install Dependencies

```bash
# Install required Python packages
pip install requests

# Set up a local LLM backend (choose one):

# Option A: Ollama (Recommended)
curl -fsSL https://ollama.ai/install.sh | sh
ollama pull llama3.2:3b
ollama serve

# Option B: llama.cpp
# Download and compile llama.cpp, then run:
# ./server -m your-model.gguf

# Option C: LM Studio
# Download and install LM Studio, then start local server
```

### 2. Basic Usage

```python
from qiling import Qiling
from qiling.extensions.llm_analyzer import QlLLMAnalyzer, LLMConfig

# Configure LLM
config = LLMConfig(
    backend="ollama",
    model="llama3.2:3b",
    base_url="http://localhost:11434"
)

# Initialize analyzer
analyzer = QlLLMAnalyzer(config)

# Run Qiling analysis
ql = Qiling(["./sample.exe"], "./rootfs", verbose=False)
ql.run()

# Generate LLM analysis
result = analyzer.analyze_qiling_instance(ql, "security_assessment")

# Save results
analyzer.save_analysis(result, "analysis_report.md")
```

### 3. Report Enhancement

```python
from qiling.extensions.llm_analyzer import QlReportEnhancer
from qiling.extensions.report import generate_report

# Generate base report
ql = Qiling(["./sample.exe"], "./rootfs")
ql.run()
base_report = generate_report(ql)

# Enhance with LLM insights
enhancer = QlReportEnhancer()
enhanced_report = enhancer.enhance_report(
    base_report, 
    enhancement_types=['security', 'behavior', 'recommendations']
)

# Create formatted summary
summary = enhancer.create_enhanced_summary(enhanced_report)
print(summary)
```

## Configuration Management

### Using Configuration Manager

```python
from qiling.extensions.llm_analyzer.config_manager import ConfigManager

config_manager = ConfigManager()

# List available templates
templates = config_manager.list_templates()
print("Available templates:", [t['name'] for t in templates])

# Create configuration from template
config = config_manager.create_config(
    "my_security_config", 
    "security_focused",
    custom_settings={"temperature": 0.05}
)

# Load existing configuration
config = config_manager.load_config("my_security_config")

# Validate configuration
validation = config_manager.validate_config(config)
if validation['valid']:
    print("Configuration is valid")
else:
    print("Errors:", validation['errors'])
```

### Available Configuration Templates

- **ollama_local**: Standard Ollama setup with 3B model
- **ollama_large**: Ollama with 8B model for detailed analysis
- **llamacpp_local**: Local llama.cpp server setup
- **lmstudio**: LM Studio compatible configuration
- **security_focused**: Optimized for security analysis
- **fast_analysis**: Quick analysis with smaller model

## Analysis Templates

### Available Templates

1. **comprehensive_analysis**: Complete analysis covering all aspects
2. **security_assessment**: Security-focused threat analysis
3. **malware_analysis**: Detailed malware behavior analysis
4. **behavior_analysis**: Program functionality and behavior
5. **shellcode_analysis**: Specialized shellcode analysis
6. **ransomware_analysis**: Ransomware-specific analysis

### Custom Templates

```python
from qiling.extensions.llm_analyzer.templates import AnalysisTemplate

custom_template = AnalysisTemplate(
    name="Custom Analysis",
    description="My custom analysis template",
    system_prompt="You are a specialized analyst...",
    user_prompt_template="Analyze this binary: {filename}...",
    analysis_type="custom"
)
```

## Supported LLM Backends

### Ollama
```python
config = LLMConfig(
    backend="ollama",
    model="llama3.2:3b",
    base_url="http://localhost:11434"
)
```

### llama.cpp
```python
config = LLMConfig(
    backend="llamacpp",
    model="llama-3.2-3b-instruct",
    base_url="http://localhost:8080"
)
```

### OpenAI-Compatible (LM Studio, etc.)
```python
config = LLMConfig(
    backend="openai-compatible",
    model="llama-3.2-3b-instruct",
    base_url="http://localhost:1234",
    api_key="your-api-key"  # if required
)
```

## Examples

See the `examples/` directory for complete examples:

- `llm_analysis_basic.py`: Basic usage example
- `llm_analysis_advanced.py`: Advanced features demonstration

## Testing

```bash
# Run unit tests
python -m unittest tests.test_llm_analyzer

# Run system validation
python tests/test_llm_analyzer.py --validate

# Check LLM backend availability
python examples/llm_analysis_basic.py --check
```

## Configuration Options

### LLMConfig Parameters

- `backend`: LLM backend type ("ollama", "llamacpp", "openai-compatible")
- `model`: Model name to use
- `base_url`: Base URL for the LLM API
- `api_key`: API key (if required)
- `temperature`: Sampling temperature (0.0-2.0)
- `max_tokens`: Maximum tokens to generate
- `top_p`: Top-p sampling parameter
- `analysis_depth`: Analysis depth ("basic", "standard", "deep")
- `output_format`: Output format ("json", "markdown", "html")
- `include_raw_data`: Include raw data in output
- `verbose_output`: Enable verbose output

## Troubleshooting

### Common Issues

1. **LLM Backend Not Available**
   ```
   Error: LLM backend 'ollama' is not available
   ```
   - Ensure your LLM backend is running
   - Check the base_url is correct
   - Verify the model is installed

2. **Analysis Fails**
   ```
   Error: LLM analysis failed
   ```
   - Check model has enough context length
   - Reduce max_tokens if needed
   - Try a different temperature setting

3. **Configuration Errors**
   ```
   Error: Configuration validation failed
   ```
   - Use `config_manager.validate_config()` to check issues
   - Ensure all required fields are set
   - Check parameter ranges

### Performance Tips

- Use smaller models (1B-3B) for faster analysis
- Reduce `max_tokens` for quicker responses
- Use `analysis_depth="basic"` for simple analysis
- Enable batch processing for multiple files

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This extension follows the same license as the Qiling framework.
