#!/usr/bin/env python3
# 
# Cross Platform and Multi Architecture Advanced Binary Emulation Framework
# Analysis Templates
#

from typing import Dict, Any, List
from dataclasses import dataclass


@dataclass
class AnalysisTemplate:
    """Template for LLM analysis prompts"""
    
    name: str
    description: str
    system_prompt: str
    user_prompt_template: str
    analysis_type: str  # behavior, security, malware, general
    
    def format_prompt(self, report_data: Dict[str, Any]) -> str:
        """Format the user prompt with report data"""
        return self.user_prompt_template.format(**report_data)


# System prompts for different analysis types
SYSTEM_PROMPTS = {
    "security_analyst": """You are an expert cybersecurity analyst specializing in binary analysis and malware detection. 
Your task is to analyze execution reports from the Qiling emulation framework and provide detailed security assessments.

Focus on:
- Identifying suspicious behaviors and potential threats
- Analyzing system calls for malicious patterns
- Detecting evasion techniques and anti-analysis methods
- Extracting indicators of compromise (IOCs)
- Providing actionable security recommendations

Be thorough, accurate, and provide specific evidence for your conclusions.""",

    "malware_analyst": """You are a senior malware analyst with expertise in reverse engineering and threat intelligence.
Analyze the provided execution data to identify malware characteristics and behaviors.

Key areas to examine:
- Malware family classification
- Persistence mechanisms
- Network communications
- File system modifications
- Registry changes (Windows)
- Evasion and obfuscation techniques
- Payload delivery methods

Provide detailed technical analysis with specific examples from the execution data.""",

    "behavior_analyst": """You are a behavioral analysis expert specializing in understanding program execution patterns.
Your role is to analyze the execution flow and identify the primary behaviors and functionalities.

Analyze:
- Program execution flow and logic
- API usage patterns
- Resource access patterns
- Inter-process communications
- File and network operations
- Overall program purpose and functionality

Provide clear explanations of what the program does and how it operates.""",

    "general_analyst": """You are a comprehensive binary analysis expert. Analyze the provided execution report 
and provide a thorough assessment covering multiple aspects including security, behavior, and technical details.

Your analysis should cover:
- Overall program functionality
- Security implications
- Technical implementation details
- Potential risks or concerns
- Recommendations for further analysis

Be comprehensive but concise, focusing on the most important findings."""
}

# Template definitions
ANALYSIS_TEMPLATES = {
    "security_assessment": AnalysisTemplate(
        name="Security Assessment",
        description="Comprehensive security analysis focusing on threats and vulnerabilities",
        system_prompt=SYSTEM_PROMPTS["security_analyst"],
        user_prompt_template="""
Analyze this binary execution report for security implications:

**Binary Information:**
- Filename: {filename}
- Architecture: {arch}
- Operating System: {os}
- Entry Point: {entry_point}

**System Calls Executed:**
{syscalls_summary}

**Strings Extracted:**
{strings_summary}

**Registry Access (Windows):**
{registry_summary}

**Import/Export Symbols:**
{symbols_summary}

**Analysis Request:**
Provide a detailed security assessment including:
1. Threat level assessment (Low/Medium/High/Critical)
2. Identified suspicious behaviors
3. Potential attack vectors
4. IOCs (Indicators of Compromise)
5. Recommended mitigation strategies
6. Suggestions for further analysis

Format your response in clear sections with specific evidence from the execution data.
""",
        analysis_type="security"
    ),

    "malware_analysis": AnalysisTemplate(
        name="Malware Analysis",
        description="Detailed malware analysis and classification",
        system_prompt=SYSTEM_PROMPTS["malware_analyst"],
        user_prompt_template="""
Analyze this execution report for malware characteristics:

**Sample Information:**
- File: {filename}
- Architecture: {arch} / {os}
- Execution Environment: {rootfs}

**Execution Trace:**
{syscalls_summary}

**String Analysis:**
{strings_summary}

**System Modifications:**
{registry_summary}

**Network/Communication Indicators:**
{network_indicators}

**Analysis Requirements:**
1. Malware family identification (if applicable)
2. Primary malicious behaviors
3. Persistence mechanisms
4. Command & Control (C2) communications
5. Data exfiltration methods
6. Anti-analysis techniques
7. MITRE ATT&CK technique mapping
8. Threat intelligence correlation

Provide detailed technical analysis with specific evidence and recommendations.
""",
        analysis_type="malware"
    ),

    "behavior_analysis": AnalysisTemplate(
        name="Behavior Analysis",
        description="Program behavior and functionality analysis",
        system_prompt=SYSTEM_PROMPTS["behavior_analyst"],
        user_prompt_template="""
Analyze the execution behavior of this binary:

**Program Details:**
- Binary: {filename}
- Platform: {arch} / {os}
- Configuration: {profile_summary}

**Execution Flow:**
{syscalls_summary}

**Resource Access:**
{strings_summary}

**System Interactions:**
{system_interactions}

**Analysis Focus:**
1. Primary program functionality
2. Execution flow and logic
3. Resource utilization patterns
4. Inter-process communications
5. File system operations
6. Network activities
7. User interface interactions
8. Overall program purpose

Explain what this program does, how it operates, and its intended functionality.
""",
        analysis_type="behavior"
    ),

    "comprehensive_analysis": AnalysisTemplate(
        name="Comprehensive Analysis",
        description="Complete analysis covering all aspects",
        system_prompt=SYSTEM_PROMPTS["general_analyst"],
        user_prompt_template="""
Perform a comprehensive analysis of this binary execution:

**Binary Information:**
- File: {filename}
- Architecture: {arch}
- Operating System: {os}
- Root Filesystem: {rootfs}
- Environment: {env_summary}

**Execution Data:**
{syscalls_summary}

**Extracted Strings:**
{strings_summary}

**System State Changes:**
{registry_summary}

**Symbol Information:**
{symbols_summary}

**Configuration Profile:**
{profile_summary}

**Complete Analysis Required:**
1. **Functionality Analysis**: What does this program do?
2. **Security Assessment**: Are there any security concerns?
3. **Technical Details**: How is it implemented?
4. **Behavioral Patterns**: What are the key execution patterns?
5. **Risk Assessment**: What are the potential risks?
6. **Recommendations**: What further analysis is recommended?

Provide a thorough, well-structured analysis covering all important aspects.
""",
        analysis_type="general"
    ),

    "shellcode_analysis": AnalysisTemplate(
        name="Shellcode Analysis",
        description="Specialized analysis for shellcode samples",
        system_prompt="""You are an expert shellcode analyst specializing in analyzing malicious code payloads.
Your task is to analyze shellcode execution traces and identify the payload's purpose, techniques, and potential impact.

Focus on:
- Shellcode type and purpose identification
- Payload delivery mechanisms
- Evasion techniques used
- Target system exploitation methods
- Network communications (if any)
- Persistence mechanisms
- Anti-analysis techniques

Provide detailed technical analysis with specific evidence from the execution trace.""",
        user_prompt_template="""
Analyze this shellcode execution trace:

**Shellcode Information:**
- Sample: {filename}
- Architecture: {arch}
- Platform: {os}
- Entry Point: {entry_point}

**Execution Trace:**
{syscalls_summary}

**Extracted Strings:**
{strings_summary}

**Network Activity:**
{network_indicators}

**System Interactions:**
{system_interactions}

**Shellcode Analysis Requirements:**
1. **Shellcode Type**: Identify the type of shellcode (reverse shell, bind shell, download & execute, etc.)
2. **Exploitation Technique**: What vulnerability or technique does it exploit?
3. **Payload Analysis**: What does the payload do once executed?
4. **Evasion Methods**: What anti-analysis or evasion techniques are used?
5. **Network Communications**: Any C2 communications or network activity?
6. **Persistence**: Does it establish persistence on the target system?
7. **IOCs**: Extract indicators of compromise
8. **Mitigation**: Recommended detection and mitigation strategies

Provide detailed technical analysis with specific evidence from the execution data.
""",
        analysis_type="shellcode"
    ),

    "ransomware_analysis": AnalysisTemplate(
        name="Ransomware Analysis",
        description="Specialized analysis for ransomware samples",
        system_prompt="""You are a ransomware analysis expert specializing in identifying and analyzing ransomware behaviors.
Your task is to analyze execution traces for ransomware-specific activities and provide comprehensive threat assessment.

Focus on:
- File encryption behaviors
- Ransom note creation and display
- System modification techniques
- Persistence mechanisms
- Network communications with C2 servers
- Anti-analysis and evasion techniques
- Payment and communication methods
- System recovery prevention methods

Provide detailed analysis with specific evidence and actionable intelligence.""",
        user_prompt_template="""
Analyze this potential ransomware sample:

**Sample Information:**
- File: {filename}
- Architecture: {arch} / {os}
- Execution Environment: {rootfs}

**System Calls Analysis:**
{syscalls_summary}

**String Analysis:**
{strings_summary}

**Registry Modifications:**
{registry_summary}

**Network Communications:**
{network_indicators}

**File System Operations:**
{system_interactions}

**Ransomware Analysis Focus:**
1. **Encryption Behavior**: Evidence of file encryption activities
2. **Ransom Mechanism**: How does it demand payment and communicate with victims?
3. **Persistence**: How does it maintain presence on the system?
4. **System Modifications**: What system changes does it make?
5. **Anti-Recovery**: Methods to prevent system recovery
6. **C2 Communications**: Command and control server interactions
7. **Evasion Techniques**: Anti-analysis and detection evasion methods
8. **Attribution**: Any indicators pointing to specific ransomware families
9. **IOCs**: Complete list of indicators of compromise
10. **Response Strategy**: Recommended incident response and recovery steps

Provide comprehensive analysis with specific technical evidence.
""",
        analysis_type="ransomware"
    )
}


def get_template(template_name: str) -> AnalysisTemplate:
    """Get analysis template by name"""
    template = ANALYSIS_TEMPLATES.get(template_name)
    if not template:
        available = list(ANALYSIS_TEMPLATES.keys())
        raise ValueError(f"Template '{template_name}' not found. Available: {available}")
    return template


def list_templates() -> List[str]:
    """List available template names"""
    return list(ANALYSIS_TEMPLATES.keys())


def get_templates_by_type(analysis_type: str) -> List[AnalysisTemplate]:
    """Get all templates of a specific analysis type"""
    return [template for template in ANALYSIS_TEMPLATES.values() 
            if template.analysis_type == analysis_type]
