#!/usr/bin/env python3
#
# Cross Platform and Multi Architecture Advanced Binary Emulation Framework
#

from qiling.const import QL_ARCH
from qiling.os.posix.posix import SYSCALL_PREF

def get_syscall_mapper(archtype: QL_ARCH):
    syscall_table = {
        QL_ARCH.X8664 : x8664_syscall_table
    }[archtype]

    def __mapper(syscall_num: int) -> str:
        return f'{SYSCALL_PREF}{syscall_table[syscall_num]}'

    return __mapper

x8664_syscall_table = {
    0: 'syscall',
    1: 'exit',
    2: 'fork',
    3: 'read',
    4: 'write',
    5: 'open',
    6: 'close',
    7: 'wait4',
    9: 'link',
    10: 'unlink',
    12: 'chdir',
    13: 'fchdir',
    14: 'freebsd11_mknod',
    15: 'chmod',
    16: 'chown',
    17: 'break',
    20: 'getpid',
    21: 'mount',
    22: 'unmount',
    23: 'setuid',
    24: 'getuid',
    25: 'geteuid',
    26: 'ptrace',
    27: 'recvmsg',
    28: 'sendmsg',
    29: 'recvfrom',
    30: 'accept',
    31: 'getpeername',
    32: 'getsockname',
    33: 'access',
    34: 'chflags',
    35: 'fchflags',
    36: 'sync',
    37: 'kill',
    39: 'getppid',
    41: 'dup',
    42: 'freebsd10_pipe',
    43: 'getegid',
    44: 'profil',
    45: 'ktrace',
    47: 'getgid',
    49: 'getlogin',
    50: 'setlogin',
    51: 'acct',
    53: 'sigaltstack',
    54: 'ioctl',
    55: 'reboot',
    56: 'revoke',
    57: 'symlink',
    58: 'readlink',
    59: 'execve',
    60: 'umask',
    61: 'chroot',
    65: 'msync',
    66: 'vfork',
    69: 'sbrk',
    70: 'sstk',
    72: 'freebsd11_vadvise',
    73: 'munmap',
    74: 'mprotect',
    75: 'madvise',
    78: 'mincore',
    79: 'getgroups',
    80: 'setgroups',
    81: 'getpgrp',
    82: 'setpgid',
    83: 'setitimer',
    85: 'swapon',
    86: 'getitimer',
    89: 'getdtablesize',
    90: 'dup2',
    92: 'fcntl',
    93: 'select',
    95: 'fsync',
    96: 'setpriority',
    97: 'socket',
    98: 'connect',
    100: 'getpriority',
    104: 'bind',
    105: 'setsockopt',
    106: 'listen',
    116: 'gettimeofday',
    117: 'getrusage',
    118: 'getsockopt',
    120: 'readv',
    121: 'writev',
    122: 'settimeofday',
    123: 'fchown',
    124: 'fchmod',
    126: 'setreuid',
    127: 'setregid',
    128: 'rename',
    131: 'flock',
    132: 'mkfifo',
    133: 'sendto',
    134: 'shutdown',
    135: 'socketpair',
    136: 'mkdir',
    137: 'rmdir',
    138: 'utimes',
    140: 'adjtime',
    147: 'setsid',
    148: 'quotactl',
    154: 'nlm_syscall',
    155: 'nfssvc',
    160: 'lgetfh',
    161: 'getfh',
    165: 'sysarch',
    166: 'rtprio',
    169: 'semsys',
    170: 'msgsys',
    171: 'shmsys',
    175: 'setfib',
    176: 'ntp_adjtime',
    181: 'setgid',
    182: 'setegid',
    183: 'seteuid',
    188: 'freebsd11_stat',
    189: 'freebsd11_fstat',
    190: 'freebsd11_lstat',
    191: 'pathconf',
    192: 'fpathconf',
    194: 'getrlimit',
    195: 'setrlimit',
    196: 'freebsd11_getdirentries',
    198: '__syscall',
    202: '__sysctl',
    203: 'mlock',
    204: 'munlock',
    205: 'undelete',
    206: 'futimes',
    207: 'getpgid',
    209: 'poll',
    220: 'freebsd7___semctl',
    221: 'semget',
    222: 'semop',
    224: 'freebsd7_msgctl',
    225: 'msgget',
    226: 'msgsnd',
    227: 'msgrcv',
    228: 'shmat',
    229: 'freebsd7_shmctl',
    230: 'shmdt',
    231: 'shmget',
    232: 'clock_gettime',
    233: 'clock_settime',
    234: 'clock_getres',
    235: 'ktimer_create',
    236: 'ktimer_delete',
    237: 'ktimer_settime',
    238: 'ktimer_gettime',
    239: 'ktimer_getoverrun',
    240: 'nanosleep',
    241: 'ffclock_getcounter',
    242: 'ffclock_setestimate',
    243: 'ffclock_getestimate',
    244: 'clock_nanosleep',
    247: 'clock_getcpuclockid2',
    248: 'ntp_gettime',
    250: 'minherit',
    251: 'rfork',
    253: 'issetugid',
    254: 'lchown',
    255: 'aio_read',
    256: 'aio_write',
    257: 'lio_listio',
    272: 'freebsd11_getdents',
    274: 'lchmod',
    276: 'lutimes',
    278: 'freebsd11_nstat',
    279: 'freebsd11_nfstat',
    280: 'freebsd11_nlstat',
    289: 'preadv',
    290: 'pwritev',
    298: 'fhopen',
    299: 'freebsd11_fhstat',
    300: 'modnext',
    301: 'modstat',
    302: 'modfnext',
    303: 'modfind',
    304: 'kldload',
    305: 'kldunload',
    306: 'kldfind',
    307: 'kldnext',
    308: 'kldstat',
    309: 'kldfirstmod',
    310: 'getsid',
    311: 'setresuid',
    312: 'setresgid',
    314: 'aio_return',
    315: 'aio_suspend',
    316: 'aio_cancel',
    317: 'aio_error',
    321: 'yield',
    324: 'mlockall',
    325: 'munlockall',
    326: '__getcwd',
    327: 'sched_setparam',
    328: 'sched_getparam',
    329: 'sched_setscheduler',
    330: 'sched_getscheduler',
    331: 'sched_yield',
    332: 'sched_get_priority_max',
    333: 'sched_get_priority_min',
    334: 'sched_rr_get_interval',
    335: 'utrace',
    337: 'kldsym',
    338: 'jail',
    339: 'nnpfs_syscall',
    340: 'sigprocmask',
    341: 'sigsuspend',
    343: 'sigpending',
    345: 'sigtimedwait',
    346: 'sigwaitinfo',
    347: '__acl_get_file',
    348: '__acl_set_file',
    349: '__acl_get_fd',
    350: '__acl_set_fd',
    351: '__acl_delete_file',
    352: '__acl_delete_fd',
    353: '__acl_aclcheck_file',
    354: '__acl_aclcheck_fd',
    355: 'extattrctl',
    356: 'extattr_set_file',
    357: 'extattr_get_file',
    358: 'extattr_delete_file',
    359: 'aio_waitcomplete',
    360: 'getresuid',
    361: 'getresgid',
    362: 'kqueue',
    363: 'freebsd11_kevent',
    371: 'extattr_set_fd',
    372: 'extattr_get_fd',
    373: 'extattr_delete_fd',
    374: '__setugid',
    376: 'eaccess',
    377: 'afs3_syscall',
    378: 'nmount',
    384: '__mac_get_proc',
    385: '__mac_set_proc',
    386: '__mac_get_fd',
    387: '__mac_get_file',
    388: '__mac_set_fd',
    389: '__mac_set_file',
    390: 'kenv',
    391: 'lchflags',
    392: 'uuidgen',
    393: 'sendfile',
    394: 'mac_syscall',
    395: 'freebsd11_getfsstat',
    396: 'freebsd11_statfs',
    397: 'freebsd11_fstatfs',
    398: 'freebsd11_fhstatfs',
    400: 'ksem_close',
    401: 'ksem_post',
    402: 'ksem_wait',
    403: 'ksem_trywait',
    404: 'ksem_init',
    405: 'ksem_open',
    406: 'ksem_unlink',
    407: 'ksem_getvalue',
    408: 'ksem_destroy',
    409: '__mac_get_pid',
    410: '__mac_get_link',
    411: '__mac_set_link',
    412: 'extattr_set_link',
    413: 'extattr_get_link',
    414: 'extattr_delete_link',
    415: '__mac_execve',
    416: 'sigaction',
    417: 'sigreturn',
    421: 'getcontext',
    422: 'setcontext',
    423: 'swapcontext',
    424: 'swapoff',
    425: '__acl_get_link',
    426: '__acl_set_link',
    427: '__acl_delete_link',
    428: '__acl_aclcheck_link',
    429: 'sigwait',
    430: 'thr_create',
    431: 'thr_exit',
    432: 'thr_self',
    433: 'thr_kill',
    436: 'jail_attach',
    437: 'extattr_list_fd',
    438: 'extattr_list_file',
    439: 'extattr_list_link',
    441: 'ksem_timedwait',
    442: 'thr_suspend',
    443: 'thr_wake',
    444: 'kldunloadf',
    445: 'audit',
    446: 'auditon',
    447: 'getauid',
    448: 'setauid',
    449: 'getaudit',
    450: 'setaudit',
    451: 'getaudit_addr',
    452: 'setaudit_addr',
    453: 'auditctl',
    454: '_umtx_op',
    455: 'thr_new',
    456: 'sigqueue',
    457: 'kmq_open',
    458: 'kmq_setattr',
    459: 'kmq_timedreceive',
    460: 'kmq_timedsend',
    461: 'kmq_notify',
    462: 'kmq_unlink',
    463: 'abort2',
    464: 'thr_set_name',
    465: 'aio_fsync',
    466: 'rtprio_thread',
    471: 'sctp_peeloff',
    472: 'sctp_generic_sendmsg',
    473: 'sctp_generic_sendmsg_iov',
    474: 'sctp_generic_recvmsg',
    475: 'pread',
    476: 'pwrite',
    477: 'mmap',
    478: 'lseek',
    479: 'truncate',
    480: 'ftruncate',
    481: 'thr_kill2',
    482: 'shm_open',
    483: 'shm_unlink',
    484: 'cpuset',
    485: 'cpuset_setid',
    486: 'cpuset_getid',
    487: 'cpuset_getaffinity',
    488: 'cpuset_setaffinity',
    489: 'faccessat',
    490: 'fchmodat',
    491: 'fchownat',
    492: 'fexecve',
    493: 'freebsd11_fstatat',
    494: 'futimesat',
    495: 'linkat',
    496: 'mkdirat',
    497: 'mkfifoat',
    498: 'freebsd11_mknodat',
    499: 'openat',
    500: 'readlinkat',
    501: 'renameat',
    502: 'symlinkat',
    503: 'unlinkat',
    504: 'posix_openpt',
    505: 'gssd_syscall',
    506: 'jail_get',
    507: 'jail_set',
    508: 'jail_remove',
    509: 'closefrom',
    510: '__semctl',
    511: 'msgctl',
    512: 'shmctl',
    513: 'lpathconf',
    515: '__cap_rights_get',
    516: 'cap_enter',
    517: 'cap_getmode',
    518: 'pdfork',
    519: 'pdkill',
    520: 'pdgetpid',
    522: 'pselect',
    523: 'getloginclass',
    524: 'setloginclass',
    525: 'rctl_get_racct',
    526: 'rctl_get_rules',
    527: 'rctl_get_limits',
    528: 'rctl_add_rule',
    529: 'rctl_remove_rule',
    530: 'posix_fallocate',
    531: 'posix_fadvise',
    532: 'wait6',
    533: 'cap_rights_limit',
    534: 'cap_ioctls_limit',
    535: 'cap_ioctls_get',
    536: 'cap_fcntls_limit',
    537: 'cap_fcntls_get',
    538: 'bindat',
    539: 'connectat',
    540: 'chflagsat',
    541: 'accept4',
    542: 'pipe2',
    543: 'aio_mlock',
    544: 'procctl',
    545: 'ppoll',
    546: 'futimens',
    547: 'utimensat',
    550: 'fdatasync',
    551: 'fstat',
    552: 'fstatat',
    553: 'fhstat',
    554: 'getdirentries',
    555: 'statfs',
    556: 'fstatfs',
    557: 'getfsstat',
    558: 'fhstatfs',
    559: 'mknodat',
    560: 'kevent',
    561: 'cpuset_getdomain',
    562: 'cpuset_setdomain',
    563: 'getrandom',
    564: 'getfhat',
    565: 'fhlink',
    566: 'fhlinkat',
    567: 'fhreadlink',
    570: '__sysctlbyname',
    575: 'close_range',
    576: 'MAXSYSCALL',
}
