#!/usr/bin/env python3
#
# Cross Platform and Multi Architecture Advanced Binary Emulation Framework
#

from unicorn.mips_const import *

reg_map = {
    "r0":  UC_MIPS_REG_0,
    "r1":  UC_MIPS_REG_1,
    "r2":  UC_MIPS_REG_2,
    "r3":  UC_MIPS_REG_3,
    "r4":  UC_MIPS_REG_4,
    "r5":  UC_MIPS_REG_5,
    "r6":  UC_MIPS_REG_6,
    "r7":  UC_MIPS_REG_7,
    "r8":  UC_MIPS_REG_8,
    "r9":  UC_MIPS_REG_9,
    "r10": UC_MIPS_REG_10,
    "r11": UC_MIPS_REG_11,
    "r12": UC_MIPS_REG_12,
    "r13": UC_MIPS_REG_13,
    "r14": UC_MIPS_REG_14,
    "r15": UC_MIPS_REG_15,
    "r16": UC_MIPS_REG_16,
    "r17": UC_MIPS_REG_17,
    "r18": UC_MIPS_REG_18,
    "r19": UC_MIPS_REG_19,
    "r20": UC_MIPS_REG_20,
    "r21": UC_MIPS_REG_21,
    "r22": UC_MIPS_REG_22,
    "r23": UC_MIPS_REG_23,
    "r24": UC_MIPS_REG_24,
    "r25": UC_MIPS_REG_25,
    "r26": UC_MIPS_REG_26,
    "r27": UC_MIPS_REG_27,
    "r28": UC_MIPS_REG_28,
    "r29": UC_MIPS_REG_29,
    "r30": UC_MIPS_REG_30,
    "r31": UC_MIPS_REG_31,

    # aliases
    "pc": UC_MIPS_REG_PC,
    "zero": UC_MIPS_REG_ZERO,
    "at": UC_MIPS_REG_AT,
    "v0": UC_MIPS_REG_V0,
    "v1": UC_MIPS_REG_V1,
    "a0": UC_MIPS_REG_A0,
    "a1": UC_MIPS_REG_A1,
    "a2": UC_MIPS_REG_A2,
    "a3": UC_MIPS_REG_A3,
    "t0": UC_MIPS_REG_T0,
    "t1": UC_MIPS_REG_T1,
    "t2": UC_MIPS_REG_T2,
    "t3": UC_MIPS_REG_T3,
    "t4": UC_MIPS_REG_T4,
    "t5": UC_MIPS_REG_T5,
    "t6": UC_MIPS_REG_T6,
    "t7": UC_MIPS_REG_T7,
    "s0": UC_MIPS_REG_S0,
    "s1": UC_MIPS_REG_S1,
    "s2": UC_MIPS_REG_S2,
    "s3": UC_MIPS_REG_S3,
    "s4": UC_MIPS_REG_S4,
    "s5": UC_MIPS_REG_S5,
    "s6": UC_MIPS_REG_S6,
    "s7": UC_MIPS_REG_S7,
    "t8": UC_MIPS_REG_T8,
    "t9": UC_MIPS_REG_T9,
    "k0": UC_MIPS_REG_K0,
    "k1": UC_MIPS_REG_K1,
    "gp": UC_MIPS_REG_GP,
    "sp": UC_MIPS_REG_SP,
    "fp": UC_MIPS_REG_FP,
    "s8": UC_MIPS_REG_S8,
    "ra": UC_MIPS_REG_RA,

    "hi": UC_MIPS_REG_HI,
    "lo": UC_MIPS_REG_LO,

    "cp0_config3":   UC_MIPS_REG_CP0_CONFIG3,
    "cp0_userlocal": UC_MIPS_REG_CP0_USERLOCAL,
    "cp0_status":    UC_MIPS_REG_CP0_STATUS
}
