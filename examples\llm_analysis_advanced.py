#!/usr/bin/env python3
"""
Advanced example of using Qiling LLM Analyzer

This example demonstrates:
1. Configuration management
2. Report enhancement
3. Batch analysis
4. Custom analysis workflows
5. Integration with existing Qiling workflows
"""

import sys
import os
import json
from pathlib import Path

# Add qiling to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from qiling import Qiling
from qiling.extensions.llm_analyzer import Ql<PERSON><PERSON><PERSON><PERSON><PERSON>, QlReportEnhancer
from qiling.extensions.llm_analyzer.config_manager import ConfigManager
from qiling.extensions.report import generate_report


def setup_configurations():
    """Set up different analysis configurations"""
    print("Setting up analysis configurations...")
    
    config_manager = ConfigManager()
    
    # Create different configurations for different use cases
    configs = {
        "security_analysis": config_manager.create_config(
            "security_analysis", 
            "security_focused",
            {
                "analysis_depth": "deep",
                "include_security_assessment": True,
                "include_recommendations": True,
                "temperature": 0.05  # More focused for security analysis
            }
        ),
        "malware_analysis": config_manager.create_config(
            "malware_analysis",
            "ollama_large", 
            {
                "analysis_depth": "deep",
                "include_code_analysis": True,
                "verbose_output": True,
                "temperature": 0.1
            }
        ),
        "quick_triage": config_manager.create_config(
            "quick_triage",
            "fast_analysis",
            {
                "analysis_depth": "basic",
                "max_tokens": 1024,
                "temperature": 0.2
            }
        )
    }
    
    print(f"Created {len(configs)} configurations:")
    for name, config in configs.items():
        print(f"  - {name}: {config.model} ({config.backend})")
    
    return configs


def analyze_with_enhancement(binary_path, rootfs_path):
    """Demonstrate report enhancement workflow"""
    print(f"\n=== Enhanced Analysis Workflow ===")
    
    # Step 1: Run basic Qiling analysis
    print("1. Running Qiling emulation...")
    ql = Qiling([binary_path], rootfs_path, verbose=False)
    ql.run()
    
    # Step 2: Generate base report
    print("2. Generating base report...")
    base_report = generate_report(ql)
    
    # Step 3: Enhance report with LLM insights
    print("3. Enhancing report with LLM analysis...")
    enhancer = QlReportEnhancer()
    
    if not enhancer.analyzer.is_ready():
        print("   LLM enhancer not ready, skipping enhancement")
        return base_report
    
    # Enhance with different types of analysis
    enhancement_types = ['summary', 'security', 'behavior', 'recommendations']
    enhanced_report = enhancer.enhance_report(base_report, enhancement_types)
    
    # Step 4: Create formatted summary
    print("4. Creating enhanced summary...")
    summary = enhancer.create_enhanced_summary(enhanced_report)
    
    # Save enhanced report
    output_path = Path("analysis_output/enhanced_report.json")
    output_path.parent.mkdir(exist_ok=True)
    
    with open(output_path, 'w') as f:
        json.dump(enhanced_report, f, indent=2, default=str)
    
    # Save summary
    summary_path = Path("analysis_output/enhanced_summary.md")
    with open(summary_path, 'w') as f:
        f.write(summary)
    
    print(f"   Enhanced report saved: {output_path}")
    print(f"   Summary saved: {summary_path}")
    
    return enhanced_report


def batch_analysis_example(binary_paths, rootfs_path):
    """Demonstrate batch analysis of multiple binaries"""
    print(f"\n=== Batch Analysis Example ===")
    
    if not binary_paths:
        print("No binaries provided for batch analysis")
        return
    
    # Use quick triage configuration for batch processing
    config_manager = ConfigManager()
    
    try:
        config = config_manager.load_config("quick_triage")
    except FileNotFoundError:
        print("Quick triage config not found, using default")
        from qiling.extensions.llm_analyzer import LLMConfig
        config = LLMConfig(backend="ollama", model="llama3.2:1b")
    
    analyzer = QlLLMAnalyzer(config)
    
    if not analyzer.is_ready():
        print("LLM analyzer not ready for batch analysis")
        return
    
    print(f"Analyzing {len(binary_paths)} binaries...")
    
    reports = []
    
    for i, binary_path in enumerate(binary_paths):
        print(f"  Processing {i+1}/{len(binary_paths)}: {Path(binary_path).name}")
        
        try:
            # Run Qiling
            ql = Qiling([binary_path], rootfs_path, verbose=False)
            ql.run()
            
            # Generate report
            report = generate_report(ql)
            reports.append(report)
            
        except Exception as e:
            print(f"    Failed to analyze {binary_path}: {e}")
            continue
    
    if not reports:
        print("No reports generated for batch analysis")
        return
    
    # Analyze all reports in batch
    print(f"Running LLM analysis on {len(reports)} reports...")
    batch_results = analyzer.analyze_multiple_reports(reports, "behavior_analysis")
    
    # Save batch results
    batch_output = Path("analysis_output/batch_analysis.json")
    batch_output.parent.mkdir(exist_ok=True)
    
    with open(batch_output, 'w') as f:
        json.dump(batch_results, f, indent=2, default=str)
    
    print(f"Batch analysis saved: {batch_output}")
    
    # Create summary report
    summary_lines = ["# Batch Analysis Summary\n"]
    
    for i, result in enumerate(batch_results):
        if 'error' in result:
            summary_lines.append(f"## Sample {i+1}: ERROR")
            summary_lines.append(f"Error: {result['error']}\n")
        else:
            metadata = result.get('metadata', {})
            analysis = result.get('analysis', {})
            
            summary_lines.append(f"## Sample {i+1}")
            summary_lines.append(f"- Analysis Time: {metadata.get('analysis_time_seconds', 'Unknown')}s")
            summary_lines.append(f"- Template: {metadata.get('template_used', 'Unknown')}")
            
            # Add first 200 chars of analysis
            content = analysis.get('content', 'No content')
            preview = content[:200] + "..." if len(content) > 200 else content
            summary_lines.append(f"- Preview: {preview}\n")
    
    summary_path = Path("analysis_output/batch_summary.md")
    with open(summary_path, 'w') as f:
        f.write('\n'.join(summary_lines))
    
    print(f"Batch summary saved: {summary_path}")


def configuration_management_demo():
    """Demonstrate configuration management features"""
    print(f"\n=== Configuration Management Demo ===")
    
    config_manager = ConfigManager()
    
    # List available templates
    print("Available configuration templates:")
    templates = config_manager.list_templates()
    for template in templates:
        print(f"  - {template['name']}: {template['description']}")
    
    # List existing configurations
    print(f"\nExisting configurations:")
    configs = config_manager.list_configs()
    for config_name in configs:
        status = config_manager.get_config_status(config_name)
        status_str = "✓" if status['available'] else "✗"
        print(f"  {status_str} {config_name}")
    
    # Validate configurations
    print(f"\nConfiguration validation:")
    for config_name in configs:
        try:
            config = config_manager.load_config(config_name)
            validation = config_manager.validate_config(config)
            
            if validation['valid']:
                print(f"  ✓ {config_name}: Valid")
            else:
                print(f"  ✗ {config_name}: Invalid")
                for error in validation['errors']:
                    print(f"    - Error: {error}")
                for warning in validation['warnings']:
                    print(f"    - Warning: {warning}")
                    
        except Exception as e:
            print(f"  ✗ {config_name}: Failed to load - {e}")


def main():
    """Main advanced example"""
    
    # Example binaries (replace with your binaries)
    binary_paths = [
        "examples/rootfs/x8664_windows/bin/x8664_hello.exe",
        # Add more binaries here for batch analysis
    ]
    rootfs_path = "examples/rootfs/x8664_windows"
    
    # Check if primary binary exists
    if not os.path.exists(binary_paths[0]):
        print(f"Primary binary not found: {binary_paths[0]}")
        print("Please provide valid binary paths")
        return
    
    print("=== Advanced Qiling LLM Analysis ===\n")
    
    # Demo 1: Configuration management
    configuration_management_demo()
    
    # Demo 2: Set up configurations
    configs = setup_configurations()
    
    # Demo 3: Enhanced analysis workflow
    try:
        enhanced_report = analyze_with_enhancement(binary_paths[0], rootfs_path)
        print("✓ Enhanced analysis completed")
    except Exception as e:
        print(f"✗ Enhanced analysis failed: {e}")
    
    # Demo 4: Batch analysis (if multiple binaries available)
    existing_binaries = [path for path in binary_paths if os.path.exists(path)]
    if len(existing_binaries) > 1:
        try:
            batch_analysis_example(existing_binaries, rootfs_path)
            print("✓ Batch analysis completed")
        except Exception as e:
            print(f"✗ Batch analysis failed: {e}")
    else:
        print("Skipping batch analysis (need multiple binaries)")
    
    print(f"\n=== Advanced Analysis Complete ===")
    print("Check the analysis_output directory for results")


if __name__ == "__main__":
    main()
