[tool.poetry]
urls = { homepage = "https://qiling.io" }
name = "qiling"
version = "1.4.8"
description = "Qiling is an advanced binary emulation framework that cross-platform-architecture"
authors = ["Kai<PERSON><PERSON> Lau (xwings) <<EMAIL>>"]
license = "GPLv2"
readme = "README.md"
classifiers = [
	"Development Status :: 3 - Beta",

	"Intended Audience :: Developers",

	"Topic :: Software Development :: Disassemblers",
	"Topic :: System :: Emulators",
	"Topic :: System :: Operating System",
	"Topic :: Security",
]
keywords = [
	"qiling",
	"QEMU",
	"binary",
	"emulator",
	"malware analysis",
	"UEFI",
	"IoT",
]

[tool.poetry.dependencies]
python = "^3.8"
capstone = "^4"
unicorn = "2.1.3"
pefile = ">=2022.5.30"
python-registry = "^1.3.1"
keystone-engine = "^0.9.2"
pyelftools = ">=0.28"
gevent = ">=20.9.0"
multiprocess = ">=*********"
windows-curses = { version = "^2.1.0", markers = "platform_system == 'Windows'" }
pyyaml = "^6.0.1"
python-fx = "*"
questionary = "*"
termcolor = "*"

unicornafl = { version = "^2.0.0", markers = "platform_system != 'Windows'", optional = true }
fuzzercorn = { version = "^0.0.1", markers = "platform_system == 'Linux'", optional = true }

r2libr = { version = "^5.7.4", optional = true }

[tool.poetry.extras]
fuzz = ["unicornafl", "fuzzercorn"]
RE = ["r2libr"]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
