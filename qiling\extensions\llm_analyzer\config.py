#!/usr/bin/env python3
# 
# Cross Platform and Multi Architecture Advanced Binary Emulation Framework
# LLM Configuration System
#

import json
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class LLMConfig:
    """Configuration for LLM integration"""
    
    # LLM Backend Configuration
    backend: str = "ollama"  # ollama, llamacpp, openai-compatible
    model: str = "llama3.2:3b"  # model name
    base_url: str = "http://localhost:11434"  # API endpoint
    api_key: Optional[str] = None  # API key if needed
    
    # Generation Parameters
    temperature: float = 0.1  # Lower for more focused analysis
    max_tokens: int = 4096
    top_p: float = 0.9
    
    # Analysis Configuration
    analysis_depth: str = "standard"  # basic, standard, deep
    include_code_analysis: bool = True
    include_security_assessment: bool = True
    include_recommendations: bool = True
    
    # Output Configuration
    output_format: str = "markdown"  # markdown, json, html
    include_raw_data: bool = False
    verbose_output: bool = True
    
    @classmethod
    def from_file(cls, config_path: str) -> 'LLMConfig':
        """Load configuration from JSON file"""
        if not os.path.exists(config_path):
            # Create default config file
            default_config = cls()
            default_config.save_to_file(config_path)
            return default_config
            
        with open(config_path, 'r') as f:
            config_data = json.load(f)
        
        return cls(**config_data)
    
    def save_to_file(self, config_path: str) -> None:
        """Save configuration to JSON file"""
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        with open(config_path, 'w') as f:
            json.dump(asdict(self), f, indent=2)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)


# Default configuration paths
DEFAULT_CONFIG_DIR = os.path.expanduser("~/.qiling/llm_analyzer")
DEFAULT_CONFIG_PATH = os.path.join(DEFAULT_CONFIG_DIR, "config.json")


def get_default_config() -> LLMConfig:
    """Get default configuration, loading from file if exists"""
    return LLMConfig.from_file(DEFAULT_CONFIG_PATH)
