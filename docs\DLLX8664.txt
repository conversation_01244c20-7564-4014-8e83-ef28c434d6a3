Windows x86_64 DLL
==================

Filename: advapi32.dll
Version: 10.0.17134.471 (WinBuild.160101.0800)
MD5: c102a6ff0fe651242be9a4be3e579106
SHA1: 86de00dcf65b3ac656158b829053afc368bd647f
SHA256: ef117b762c2c680d181cf4119ff611c9de46fcea6b60775e746541f5dd8f1cd0

Filename: kernel32.dll
Version: 10.0.17134.1 (WinBuild.160101.0800)
MD5: a8565440629ac87f6fef7d588fe3ff0f
SHA1: 51a5d214c185e50804411b647c8ce42567566ce4
SHA256: 7d148e220040de2fae1439fbc0e783ef344dceaea4757611722d8378a4938d0b

Filename: KernelBase.dll
Version: 10.0.17134.441 (WinBuild.160101.0800)
MD5: 7b4d73316a4e7e0f61094be702e353bd
SHA1: aabc2024c896649b8a08353e02378c1adf3e6ca4
SHA256: d021b60a549856f67814ec609499972f0153f60c79e514e06b5cc88d5eb788d1

Filename: msvcrt.dll
Version: 7.0.7600.16385 (win7_rtm.090713-1255)
MD5: 7319bb10fa1f86e49e3dcf4136f6c957  
SHA1: 3eea5ee8bafb2b9975b236c5c5655df6f4b42aa1
SHA256: 60de43ab267fd41c9804369b569139add30ed4e295c425f44fc04d3fcc95fca2

Filename: ntdll.dll
Version: 10.0.17134.471 (WinBuild.160101.0800)
MD5: 7b96ea2c7afa2654edd63632791ba0be
SHA1: 474853b8a93b39e038291ae0f4ae4422152cea03
SHA256: b579592d883de9f4453b74a90a8166db4b0a7617fd815fe658291d3afc44809e

Filename: urlmon.dll
Version: 11.00.17134.407 (WinBuild.160101.0800)
MD5: d59c3dc4f99d23e0ef5694b9f69b0981
SHA1: eca60a7b603aa131aa293d8d82f980a695e20635
SHA256: 896be11f79e076f2d205a915bb8470b507f52bd40fbf3575293eda902c67103a

Filename: user32.dll
File Version: 10.0.17134.376 (WinBuild.160101.0800)
MD5: cec499e17074bef1cf32bb0af742f2d2
SHA1: a8d593fc9a44ccc5c17696d1b44ba59481b816e0
SHA256: 8733a580442177d5820bfa6592bc593d09f8eb3944231130473e53a9bdfd775c

Filename: ws2_32.dll
Version: 10.0.17134.1 (WinBuild.160101.0800)
MD5: 6013120b6b147b2584927639ee70fb4f
SHA1: 5d8508ba30453c7ecfdd47e29fd36210f1e3bb3c
SHA256: e52b017d1475bf07cb9652418e4c8cecc739c06abc446ed7e0e5d9831d225b85

Filename: vcruntime140.dll

