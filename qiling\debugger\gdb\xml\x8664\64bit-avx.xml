<?xml version="1.0"?>
<!-- Copyright (C) 2010-2020 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->

<!DOCTYPE feature SYSTEM "gdb-target.dtd">
<feature name="org.gnu.gdb.i386.avx">
  <reg name="ymm0h" bitsize="128" type="uint128"/>
  <reg name="ymm1h" bitsize="128" type="uint128"/>
  <reg name="ymm2h" bitsize="128" type="uint128"/>
  <reg name="ymm3h" bitsize="128" type="uint128"/>
  <reg name="ymm4h" bitsize="128" type="uint128"/>
  <reg name="ymm5h" bitsize="128" type="uint128"/>
  <reg name="ymm6h" bitsize="128" type="uint128"/>
  <reg name="ymm7h" bitsize="128" type="uint128"/>
  <reg name="ymm8h" bitsize="128" type="uint128"/>
  <reg name="ymm9h" bitsize="128" type="uint128"/>
  <reg name="ymm10h" bitsize="128" type="uint128"/>
  <reg name="ymm11h" bitsize="128" type="uint128"/>
  <reg name="ymm12h" bitsize="128" type="uint128"/>
  <reg name="ymm13h" bitsize="128" type="uint128"/>
  <reg name="ymm14h" bitsize="128" type="uint128"/>
  <reg name="ymm15h" bitsize="128" type="uint128"/>
</feature>
