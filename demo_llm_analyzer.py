#!/usr/bin/env python3
"""
Qiling LLM Analyzer Demo

This demo showcases the complete LLM-powered analysis system for Qiling.
It demonstrates all major features including:
- Configuration management
- Multiple analysis templates
- Report enhancement
- Batch processing
- Output formatting
"""

import sys
import os
from pathlib import Path

# Add qiling to path
sys.path.insert(0, os.path.dirname(__file__))

from qiling.extensions.llm_analyzer import (
    QlLLMAnalyzer, QlReportEnhancer, ConfigManager,
    quick_analyze, quick_enhance
)


def demo_configuration_management():
    """Demo configuration management features"""
    print("🔧 Configuration Management Demo")
    print("=" * 50)
    
    config_manager = ConfigManager()
    
    # Show available templates
    print("\n📋 Available Configuration Templates:")
    templates = config_manager.list_templates()
    for template in templates:
        print(f"  • {template['name']}: {template['description']}")
    
    # Create a custom configuration
    print(f"\n⚙️ Creating custom configuration...")
    try:
        config = config_manager.create_config(
            "demo_config",
            "security_focused",
            custom_settings={
                "temperature": 0.05,
                "max_tokens": 4096,
                "analysis_depth": "deep"
            }
        )
        print(f"  ✅ Created 'demo_config' successfully")
        print(f"     Backend: {config.backend}")
        print(f"     Model: {config.model}")
        print(f"     Temperature: {config.temperature}")
        
    except Exception as e:
        print(f"  ❌ Failed to create config: {e}")
    
    # List existing configurations
    print(f"\n📁 Existing Configurations:")
    configs = config_manager.list_configs()
    for config_name in configs:
        status = config_manager.get_config_status(config_name)
        status_icon = "✅" if status.get('available') else "❌"
        print(f"  {status_icon} {config_name}")


def demo_quick_functions():
    """Demo quick analysis functions"""
    print("\n\n⚡ Quick Functions Demo")
    print("=" * 50)
    
    # Example binary path (you'll need to adjust this)
    binary_path = "examples/rootfs/x8664_windows/bin/x8664_hello.exe"
    rootfs_path = "examples/rootfs/x8664_windows"
    
    if not os.path.exists(binary_path):
        print(f"❌ Demo binary not found: {binary_path}")
        print("   Please provide a valid binary path to test quick functions")
        return
    
    print(f"📁 Analyzing: {Path(binary_path).name}")
    
    # Quick analyze demo
    print(f"\n🔍 Quick Analysis...")
    try:
        result = quick_analyze(
            binary_path, 
            rootfs_path, 
            template="behavior_analysis",
            backend="ollama",
            model="llama3.2:3b"
        )
        
        print(f"  ✅ Analysis completed successfully")
        print(f"     Template: {result['metadata']['template_used']}")
        print(f"     Analysis time: {result['metadata']['analysis_time_seconds']}s")
        
        # Show preview of analysis
        content = result['analysis']['content']
        preview = content[:200] + "..." if len(content) > 200 else content
        print(f"     Preview: {preview}")
        
    except Exception as e:
        print(f"  ❌ Quick analysis failed: {e}")
    
    # Quick enhance demo
    print(f"\n✨ Quick Enhancement...")
    try:
        enhanced = quick_enhance(
            binary_path,
            rootfs_path,
            enhancement_types=['summary', 'security']
        )
        
        print(f"  ✅ Enhancement completed successfully")
        enhancements = enhanced.get('llm_enhancements', {})
        
        for enhancement_type in ['summary', 'security']:
            if enhancement_type in enhancements:
                status = enhancements[enhancement_type].get('status', 'unknown')
                print(f"     {enhancement_type.title()}: {status}")
        
    except Exception as e:
        print(f"  ❌ Quick enhancement failed: {e}")


def demo_analysis_templates():
    """Demo different analysis templates"""
    print("\n\n📝 Analysis Templates Demo")
    print("=" * 50)
    
    from qiling.extensions.llm_analyzer.templates import list_templates, get_template
    
    templates = list_templates()
    print(f"\n📋 Available Templates ({len(templates)} total):")
    
    for template_name in templates:
        template = get_template(template_name)
        print(f"\n  📄 {template.name}")
        print(f"     Type: {template.analysis_type}")
        print(f"     Description: {template.description}")
        
        # Show a snippet of the system prompt
        system_preview = template.system_prompt[:100] + "..."
        print(f"     System Prompt Preview: {system_preview}")


def demo_backend_connectivity():
    """Demo LLM backend connectivity testing"""
    print("\n\n🌐 Backend Connectivity Demo")
    print("=" * 50)
    
    import requests
    
    backends = [
        ("Ollama", "http://localhost:11434/api/tags", "ollama"),
        ("llama.cpp", "http://localhost:8080/health", "llamacpp"),
        ("LM Studio", "http://localhost:1234/v1/models", "openai-compatible")
    ]
    
    available_backends = []
    
    print(f"\n🔍 Testing Backend Availability:")
    
    for name, url, backend_type in backends:
        try:
            response = requests.get(url, timeout=3)
            if response.status_code == 200:
                print(f"  ✅ {name} - Available")
                available_backends.append((name, backend_type))
                
                # Try to get model info for Ollama
                if backend_type == "ollama":
                    try:
                        models = response.json().get('models', [])
                        if models:
                            model_names = [m.get('name', 'Unknown') for m in models[:3]]
                            print(f"     Models: {', '.join(model_names)}")
                    except:
                        pass
                        
            else:
                print(f"  ❌ {name} - HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ {name} - Not accessible")
    
    if available_backends:
        print(f"\n✅ {len(available_backends)} backend(s) available for analysis")
        
        # Test analyzer initialization with first available backend
        name, backend_type = available_backends[0]
        print(f"\n🧪 Testing Analyzer with {name}...")
        
        try:
            from qiling.extensions.llm_analyzer import LLMConfig, QlLLMAnalyzer
            
            config = LLMConfig(backend=backend_type)
            analyzer = QlLLMAnalyzer(config)
            
            if analyzer.is_ready():
                print(f"  ✅ Analyzer ready with {name}")
            else:
                print(f"  ❌ Analyzer not ready with {name}")
                
        except Exception as e:
            print(f"  ❌ Analyzer test failed: {e}")
    else:
        print(f"\n❌ No backends available")
        print(f"   Please start one of the following:")
        print(f"   • Ollama: ollama serve")
        print(f"   • llama.cpp: ./server -m model.gguf")
        print(f"   • LM Studio: Start LM Studio and enable local server")


def demo_system_info():
    """Show system information and capabilities"""
    print("\n\n📊 System Information")
    print("=" * 50)
    
    from qiling.extensions.llm_analyzer import __version__
    
    print(f"\n🔖 Qiling LLM Analyzer v{__version__}")
    
    # Count available components
    from qiling.extensions.llm_analyzer.templates import list_templates
    from qiling.extensions.llm_analyzer.config_manager import ConfigManager
    
    templates = list_templates()
    config_manager = ConfigManager()
    config_templates = config_manager.list_templates()
    
    print(f"\n📈 Capabilities:")
    print(f"  • Analysis Templates: {len(templates)}")
    print(f"  • Configuration Templates: {len(config_templates)}")
    print(f"  • Supported Backends: 3 (Ollama, llama.cpp, OpenAI-compatible)")
    print(f"  • Output Formats: 3 (JSON, Markdown, HTML)")
    
    print(f"\n🎯 Key Features:")
    features = [
        "Local LLM Integration",
        "Multiple Analysis Templates", 
        "Report Enhancement",
        "Batch Processing",
        "Configuration Management",
        "Flexible Output Formats"
    ]
    
    for feature in features:
        print(f"  ✅ {feature}")


def main():
    """Main demo function"""
    print("🚀 Qiling LLM Analyzer - Complete Demo")
    print("=" * 60)
    print("This demo showcases the LLM-powered analysis system for Qiling")
    print("=" * 60)
    
    # Run all demos
    demo_system_info()
    demo_backend_connectivity()
    demo_configuration_management()
    demo_analysis_templates()
    demo_quick_functions()
    
    print("\n\n🎉 Demo Complete!")
    print("=" * 60)
    print("Next steps:")
    print("• Check examples/ directory for detailed usage examples")
    print("• Run tests with: python -m unittest tests.test_llm_analyzer")
    print("• Read documentation: qiling/extensions/llm_analyzer/README.md")
    print("• Start analyzing your binaries with LLM-powered insights!")


if __name__ == "__main__":
    main()
