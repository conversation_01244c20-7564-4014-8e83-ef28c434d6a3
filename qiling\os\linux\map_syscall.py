#!/usr/bin/env python3
#
# Cross Platform and Multi Architecture Advanced Binary Emulation Framework
#

# Table from: 
#  - https://github.com/zeropointdynamics/zelos/blob/master/src/zelos/ext/platforms/linux/syscalls/syscalls_table.py
#  - https://github.com/hrw/python-syscalls/tree/development/system_calls/tables
# cols = ("arm64", "arm", "x8664", "x32", "x86", "mips", "powerpc", "ia64")

from qiling.const import QL_ARCH
from qiling.os.posix.posix import SYSCALL_PREF

def get_syscall_mapper(archtype: QL_ARCH):
    syscall_table = {
        QL_ARCH.ARM64   : arm64_syscall_table,
        QL_ARCH.ARM     : arm_syscall_table,
        QL_ARCH.X8664   : x8664_syscall_table,
        QL_ARCH.X86     : x86_syscall_table,
        QL_ARCH.MIPS    : mips_syscall_table,
        QL_ARCH.RISCV   : riscv32_syscall_table,
        QL_ARCH.RISCV64 : riscv64_syscall_table,
        QL_ARCH.PPC     : ppc_syscall_table
    }[archtype]

    def __mapper(syscall_num: int) -> str:
        return f'{SYSCALL_PREF}{syscall_table[syscall_num]}'

    return __mapper

arm_syscall_table = {
    0: "restart_syscall",
    1: "exit",
    2: "fork",
    3: "read",
    4: "write",
    5: "open",
    6: "close",
    8: "creat",
    9: "link",
    10: "unlink",
    11: "execve",
    12: "chdir",
    13: "time",
    14: "mknod",
    15: "chmod",
    16: "lchown",
    19: "lseek",
    20: "getpid",
    21: "mount",
    23: "setuid",
    24: "getuid",
    26: "ptrace",
    29: "pause",
    33: "access",
    34: "nice",
    36: "sync",
    37: "kill",
    38: "rename",
    39: "mkdir",
    40: "rmdir",
    41: "dup",
    42: "pipe",
    43: "times",
    45: "brk",
    46: "setgid",
    47: "getgid",
    49: "geteuid",
    50: "getegid",
    51: "acct",
    52: "umount2",
    54: "ioctl",
    55: "fcntl",
    57: "setpgid",
    60: "umask",
    61: "chroot",
    62: "ustat",
    63: "dup2",
    64: "getppid",
    65: "getpgrp",
    66: "setsid",
    67: "sigaction",
    70: "setreuid",
    71: "setregid",
    72: "sigsuspend",
    73: "sigpending",
    74: "sethostname",
    75: "setrlimit",
    77: "getrusage",
    78: "gettimeofday",
    79: "settimeofday",
    80: "getgroups",
    81: "setgroups",
    83: "symlink",
    85: "readlink",
    86: "uselib",
    87: "swapon",
    88: "reboot",
    91: "munmap",
    92: "truncate",
    93: "ftruncate",
    94: "fchmod",
    95: "fchown",
    96: "getpriority",
    97: "setpriority",
    99: "statfs",
    100: "fstatfs",
    102: "socketcall",
    103: "syslog",
    104: "setitimer",
    105: "getitimer",
    106: "stat",
    107: "lstat",
    108: "fstat",
    111: "vhangup",
    113: "syscall",
    114: "wait4",
    115: "swapoff",
    116: "sysinfo",
    118: "fsync",
    119: "sigreturn",
    120: "clone",
    121: "setdomainname",
    122: "uname",
    124: "adjtimex",
    125: "mprotect",
    126: "sigprocmask",
    128: "init_module",
    129: "delete_module",
    131: "quotactl",
    132: "getpgid",
    133: "fchdir",
    134: "bdflush",
    135: "sysfs",
    136: "personality",
    138: "setfsuid",
    139: "setfsgid",
    140: "_llseek",
    141: "getdents",
    142: "_newselect",
    143: "flock",
    144: "msync",
    145: "readv",
    146: "writev",
    147: "getsid",
    148: "fdatasync",
    149: "_sysctl",
    150: "mlock",
    151: "munlock",
    152: "mlockall",
    153: "munlockall",
    154: "sched_setparam",
    155: "sched_getparam",
    156: "sched_setscheduler",
    157: "sched_getscheduler",
    158: "sched_yield",
    159: "sched_get_priority_max",
    160: "sched_get_priority_min",
    161: "sched_rr_get_interval",
    162: "nanosleep",
    163: "mremap",
    164: "setresuid",
    165: "getresuid",
    168: "poll",
    169: "nfsservctl",
    170: "setresgid",
    171: "getresgid",
    172: "prctl",
    173: "rt_sigreturn",
    174: "rt_sigaction",
    175: "rt_sigprocmask",
    176: "rt_sigpending",
    177: "rt_sigtimedwait",
    178: "rt_sigqueueinfo",
    179: "rt_sigsuspend",
    180: "pread64",
    181: "pwrite64",
    182: "chown",
    183: "getcwd",
    184: "capget",
    185: "capset",
    186: "sigaltstack",
    187: "sendfile",
    190: "vfork",
    191: "ugetrlimit",
    192: "mmap2",
    193: "truncate64",
    194: "ftruncate64",
    195: "stat64",
    196: "lstat64",
    197: "fstat64",
    198: "lchown32",
    199: "getuid32",
    200: "getgid32",
    201: "geteuid32",
    202: "getegid32",
    203: "setreuid32",
    204: "setregid32",
    205: "getgroups32",
    206: "setgroups32",
    207: "fchown32",
    208: "setresuid32",
    209: "getresuid32",
    210: "setresgid32",
    211: "getresgid32",
    212: "chown32",
    213: "setuid32",
    214: "setgid32",
    215: "setfsuid32",
    216: "setfsgid32",
    217: "getdents64",
    218: "pivot_root",
    219: "mincore",
    220: "madvise",
    221: "fcntl64",
    224: "gettid",
    225: "readahead",
    226: "setxattr",
    227: "lsetxattr",
    228: "fsetxattr",
    229: "getxattr",
    230: "lgetxattr",
    231: "fgetxattr",
    232: "listxattr",
    233: "llistxattr",
    234: "flistxattr",
    235: "removexattr",
    236: "lremovexattr",
    237: "fremovexattr",
    238: "tkill",
    239: "sendfile64",
    240: "futex",
    241: "sched_setaffinity",
    242: "sched_getaffinity",
    243: "io_setup",
    244: "io_destroy",
    245: "io_getevents",
    246: "io_submit",
    247: "io_cancel",
    248: "exit_group",
    249: "lookup_dcookie",
    250: "epoll_create",
    251: "epoll_ctl",
    252: "epoll_wait",
    253: "remap_file_pages",
    256: "set_tid_address",
    257: "timer_create",
    258: "timer_settime",
    259: "timer_gettime",
    260: "timer_getoverrun",
    261: "timer_delete",
    262: "clock_settime",
    263: "clock_gettime",
    264: "clock_getres",
    265: "clock_nanosleep",
    266: "statfs64",
    267: "fstatfs64",
    268: "tgkill",
    269: "utimes",
    270: "arm_fadvise64_64",
    271: "pciconfig_iobase",
    272: "pciconfig_read",
    273: "pciconfig_write",
    274: "mq_open",
    275: "mq_unlink",
    276: "mq_timedsend",
    277: "mq_timedreceive",
    278: "mq_notify",
    279: "mq_getsetattr",
    280: "waitid",
    281: "socket",
    282: "bind",
    283: "connect",
    284: "listen",
    285: "accept",
    286: "getsockname",
    287: "getpeername",
    288: "socketpair",
    289: "send",
    290: "sendto",
    291: "recv",
    292: "recvfrom",
    293: "shutdown",
    294: "setsockopt",
    295: "getsockopt",
    296: "sendmsg",
    297: "recvmsg",
    298: "semop",
    299: "semget",
    300: "semctl",
    301: "msgsnd",
    302: "msgrcv",
    303: "msgget",
    304: "msgctl",
    305: "shmat",
    306: "shmdt",
    307: "shmget",
    308: "shmctl",
    309: "add_key",
    310: "request_key",
    311: "keyctl",
    312: "semtimedop",
    314: "ioprio_set",
    315: "ioprio_get",
    316: "inotify_init",
    317: "inotify_add_watch",
    318: "inotify_rm_watch",
    319: "mbind",
    320: "get_mempolicy",
    321: "set_mempolicy",
    322: "openat",
    323: "mkdirat",
    324: "mknodat",
    325: "fchownat",
    326: "futimesat",
    327: "fstatat64",
    328: "unlinkat",
    329: "renameat",
    330: "linkat",
    331: "symlinkat",
    332: "readlinkat",
    333: "fchmodat",
    334: "faccessat",
    335: "pselect6",
    336: "ppoll",
    337: "unshare",
    338: "set_robust_list",
    339: "get_robust_list",
    340: "splice",
    341: "sync_file_range2",
    342: "tee",
    343: "vmsplice",
    344: "move_pages",
    345: "getcpu",
    346: "epoll_pwait",
    347: "kexec_load",
    348: "utimensat",
    349: "signalfd",
    350: "timerfd_create",
    351: "eventfd",
    352: "fallocate",
    353: "timerfd_settime",
    354: "timerfd_gettime",
    355: "signalfd4",
    356: "eventfd2",
    357: "epoll_create1",
    358: "dup3",
    359: "pipe2",
    360: "inotify_init1",
    361: "preadv",
    362: "pwritev",
    363: "rt_tgsigqueueinfo",
    364: "perf_event_open",
    365: "recvmmsg",
    366: "accept4",
    367: "fanotify_init",
    368: "fanotify_mark",
    369: "prlimit64",
    370: "name_to_handle_at",
    371: "open_by_handle_at",
    372: "clock_adjtime",
    373: "syncfs",
    374: "sendmmsg",
    375: "setns",
    376: "process_vm_readv",
    377: "process_vm_writev",
    378: "kcmp",
    379: "finit_module",
    380: "sched_setattr",
    381: "sched_getattr",
    382: "renameat2",
    383: "seccomp",
    384: "getrandom",
    385: "memfd_create",
    386: "bpf",
    387: "execveat",
    388: "userfaultfd",
    389: "membarrier",
    390: "mlock2",
    391: "copy_file_range",
    392: "preadv2",
    393: "pwritev2",
    394: "pkey_mprotect",
    395: "pkey_alloc",
    396: "pkey_free",
    397: "statx",
    398: "rseq",
    399: "io_pgetevents",
    400: "migrate_pages",
    401: "kexec_file_load",
    403: "clock_gettime64",
    404: "clock_settime64",
    405: "clock_adjtime64",
    406: "clock_getres_time64",
    407: "clock_nanosleep_time64",
    408: "timer_gettime64",
    409: "timer_settime64",
    410: "timerfd_gettime64",
    411: "timerfd_settime64",
    412: "utimensat_time64",
    413: "pselect6_time64",
    414: "ppoll_time64",
    416: "io_pgetevents_time64",
    417: "recvmmsg_time64",
    418: "mq_timedsend_time64",
    419: "mq_timedreceive_time64",
    420: "semtimedop_time64",
    421: "rt_sigtimedwait_time64",
    422: "futex_time64",
    423: "sched_rr_get_interval_time64",
    424: "pidfd_send_signal",
    425: "io_uring_setup",
    426: "io_uring_enter",
    427: "io_uring_register",
    428: "open_tree",
    429: "move_mount",
    430: "fsopen",
    431: "fsconfig",
    432: "fsmount",
    433: "fspick",
    434: "pidfd_open",
    435: "clone3",
    436: "close_range",
    437: "openat2",
    438: "pidfd_getfd",
    439: "faccessat2",
    440: "process_madvise",
    441: "epoll_pwait2",
    442: "mount_setattr",
    443: "quotactl_fd",
    444: "landlock_create_ruleset",
    445: "landlock_add_rule",
    446: "landlock_restrict_self",
    448: "process_mrelease",
    983042: "cacheflush",
    983045: "set_tls",
}

arm64_syscall_table = {
    0: "io_setup",
    1: "io_destroy",
    2: "io_submit",
    3: "io_cancel",
    4: "io_getevents",
    5: "setxattr",
    6: "lsetxattr",
    7: "fsetxattr",
    8: "getxattr",
    9: "lgetxattr",
    10: "fgetxattr",
    11: "listxattr",
    12: "llistxattr",
    13: "flistxattr",
    14: "removexattr",
    15: "lremovexattr",
    16: "fremovexattr",
    17: "getcwd",
    18: "lookup_dcookie",
    19: "eventfd2",
    20: "epoll_create1",
    21: "epoll_ctl",
    22: "epoll_pwait",
    23: "dup",
    24: "dup3",
    25: "fcntl",
    26: "inotify_init1",
    27: "inotify_add_watch",
    28: "inotify_rm_watch",
    29: "ioctl",
    30: "ioprio_set",
    31: "ioprio_get",
    32: "flock",
    33: "mknodat",
    34: "mkdirat",
    35: "unlinkat",
    36: "symlinkat",
    37: "linkat",
    38: "renameat",
    39: "umount2",
    40: "mount",
    41: "pivot_root",
    42: "nfsservctl",
    43: "statfs",
    44: "fstatfs",
    45: "truncate",
    46: "ftruncate",
    47: "fallocate",
    48: "faccessat",
    49: "chdir",
    50: "fchdir",
    51: "chroot",
    52: "fchmod",
    53: "fchmodat",
    54: "fchownat",
    55: "fchown",
    56: "openat",
    57: "close",
    58: "vhangup",
    59: "pipe2",
    60: "quotactl",
    61: "getdents64",
    62: "lseek",
    63: "read",
    64: "write",
    65: "readv",
    66: "writev",
    67: "pread64",
    68: "pwrite64",
    69: "preadv",
    70: "pwritev",
    71: "sendfile",
    72: "pselect6",
    73: "ppoll",
    74: "signalfd4",
    75: "vmsplice",
    76: "splice",
    77: "tee",
    78: "readlinkat",
    79: "newfstatat",
    80: "fstat",
    81: "sync",
    82: "fsync",
    83: "fdatasync",
    84: "sync_file_range",
    85: "timerfd_create",
    86: "timerfd_settime",
    87: "timerfd_gettime",
    88: "utimensat",
    89: "acct",
    90: "capget",
    91: "capset",
    92: "personality",
    93: "exit",
    94: "exit_group",
    95: "waitid",
    96: "set_tid_address",
    97: "unshare",
    98: "futex",
    99: "set_robust_list",
    100: "get_robust_list",
    101: "nanosleep",
    102: "getitimer",
    103: "setitimer",
    104: "kexec_load",
    105: "init_module",
    106: "delete_module",
    107: "timer_create",
    108: "timer_gettime",
    109: "timer_getoverrun",
    110: "timer_settime",
    111: "timer_delete",
    112: "clock_settime",
    113: "clock_gettime",
    114: "clock_getres",
    115: "clock_nanosleep",
    116: "syslog",
    117: "ptrace",
    118: "sched_setparam",
    119: "sched_setscheduler",
    120: "sched_getscheduler",
    121: "sched_getparam",
    122: "sched_setaffinity",
    123: "sched_getaffinity",
    124: "sched_yield",
    125: "sched_get_priority_max",
    126: "sched_get_priority_min",
    127: "sched_rr_get_interval",
    128: "restart_syscall",
    129: "kill",
    130: "tkill",
    131: "tgkill",
    132: "sigaltstack",
    133: "rt_sigsuspend",
    134: "rt_sigaction",
    135: "rt_sigprocmask",
    136: "rt_sigpending",
    137: "rt_sigtimedwait",
    138: "rt_sigqueueinfo",
    139: "rt_sigreturn",
    140: "setpriority",
    141: "getpriority",
    142: "reboot",
    143: "setregid",
    144: "setgid",
    145: "setreuid",
    146: "setuid",
    147: "setresuid",
    148: "getresuid",
    149: "setresgid",
    150: "getresgid",
    151: "setfsuid",
    152: "setfsgid",
    153: "times",
    154: "setpgid",
    155: "getpgid",
    156: "getsid",
    157: "setsid",
    158: "getgroups",
    159: "setgroups",
    160: "uname",
    161: "sethostname",
    162: "setdomainname",
    163: "getrlimit",
    164: "setrlimit",
    165: "getrusage",
    166: "umask",
    167: "prctl",
    168: "getcpu",
    169: "gettimeofday",
    170: "settimeofday",
    171: "adjtimex",
    172: "getpid",
    173: "getppid",
    174: "getuid",
    175: "geteuid",
    176: "getgid",
    177: "getegid",
    178: "gettid",
    179: "sysinfo",
    180: "mq_open",
    181: "mq_unlink",
    182: "mq_timedsend",
    183: "mq_timedreceive",
    184: "mq_notify",
    185: "mq_getsetattr",
    186: "msgget",
    187: "msgctl",
    188: "msgrcv",
    189: "msgsnd",
    190: "semget",
    191: "semctl",
    192: "semtimedop",
    193: "semop",
    194: "shmget",
    195: "shmctl",
    196: "shmat",
    197: "shmdt",
    198: "socket",
    199: "socketpair",
    200: "bind",
    201: "listen",
    202: "accept",
    203: "connect",
    204: "getsockname",
    205: "getpeername",
    206: "sendto",
    207: "recvfrom",
    208: "setsockopt",
    209: "getsockopt",
    210: "shutdown",
    211: "sendmsg",
    212: "recvmsg",
    213: "readahead",
    214: "brk",
    215: "munmap",
    216: "mremap",
    217: "add_key",
    218: "request_key",
    219: "keyctl",
    220: "clone",
    221: "execve",
    222: "mmap",
    223: "fadvise64",
    224: "swapon",
    225: "swapoff",
    226: "mprotect",
    227: "msync",
    228: "mlock",
    229: "munlock",
    230: "mlockall",
    231: "munlockall",
    232: "mincore",
    233: "madvise",
    234: "remap_file_pages",
    235: "mbind",
    236: "get_mempolicy",
    237: "set_mempolicy",
    238: "migrate_pages",
    239: "move_pages",
    240: "rt_tgsigqueueinfo",
    241: "perf_event_open",
    242: "accept4",
    243: "recvmmsg",
    260: "wait4",
    261: "prlimit64",
    262: "fanotify_init",
    263: "fanotify_mark",
    264: "name_to_handle_at",
    265: "open_by_handle_at",
    266: "clock_adjtime",
    267: "syncfs",
    268: "setns",
    269: "sendmmsg",
    270: "process_vm_readv",
    271: "process_vm_writev",
    272: "kcmp",
    273: "finit_module",
    274: "sched_setattr",
    275: "sched_getattr",
    276: "renameat2",
    277: "seccomp",
    278: "getrandom",
    279: "memfd_create",
    280: "bpf",
    281: "execveat",
    282: "userfaultfd",
    283: "membarrier",
    284: "mlock2",
    285: "copy_file_range",
    286: "preadv2",
    287: "pwritev2",
    288: "pkey_mprotect",
    289: "pkey_alloc",
    290: "pkey_free",
    291: "statx",
    292: "io_pgetevents",
    293: "rseq",
    294: "kexec_file_load",
    424: "pidfd_send_signal",
    425: "io_uring_setup",
    426: "io_uring_enter",
    427: "io_uring_register",
    428: "open_tree",
    429: "move_mount",
    430: "fsopen",
    431: "fsconfig",
    432: "fsmount",
    433: "fspick",
    434: "pidfd_open",
    435: "clone3",
    436: "close_range",
    437: "openat2",
    438: "pidfd_getfd",
    439: "faccessat2",
    440: "process_madvise",
    441: "epoll_pwait2",
    442: "mount_setattr",
    443: "quotactl_fd",
    444: "landlock_create_ruleset",
    445: "landlock_add_rule",
    446: "landlock_restrict_self",
    447: "memfd_secret",
    448: "process_mrelease",
}

x86_syscall_table = {
    0: "restart_syscall",
    1: "exit",
    2: "fork",
    3: "read",
    4: "write",
    5: "open",
    6: "close",
    7: "waitpid",
    8: "creat",
    9: "link",
    10: "unlink",
    11: "execve",
    12: "chdir",
    13: "time",
    14: "mknod",
    15: "chmod",
    16: "lchown",
    18: "oldstat",
    19: "lseek",
    20: "getpid",
    21: "mount",
    22: "umount",
    23: "setuid",
    24: "getuid",
    25: "stime",
    26: "ptrace",
    27: "alarm",
    28: "oldfstat",
    29: "pause",
    30: "utime",
    33: "access",
    34: "nice",
    36: "sync",
    37: "kill",
    38: "rename",
    39: "mkdir",
    40: "rmdir",
    41: "dup",
    42: "pipe",
    43: "times",
    45: "brk",
    46: "setgid",
    47: "getgid",
    48: "signal",
    49: "geteuid",
    50: "getegid",
    51: "acct",
    52: "umount2",
    54: "ioctl",
    55: "fcntl",
    57: "setpgid",
    59: "oldolduname",
    60: "umask",
    61: "chroot",
    62: "ustat",
    63: "dup2",
    64: "getppid",
    65: "getpgrp",
    66: "setsid",
    67: "sigaction",
    68: "sgetmask",
    69: "ssetmask",
    70: "setreuid",
    71: "setregid",
    72: "sigsuspend",
    73: "sigpending",
    74: "sethostname",
    75: "setrlimit",
    76: "getrlimit",
    77: "getrusage",
    78: "gettimeofday",
    79: "settimeofday",
    80: "getgroups",
    81: "setgroups",
    82: "select",
    83: "symlink",
    84: "oldlstat",
    85: "readlink",
    86: "uselib",
    87: "swapon",
    88: "reboot",
    89: "readdir",
    90: "mmap",
    91: "munmap",
    92: "truncate",
    93: "ftruncate",
    94: "fchmod",
    95: "fchown",
    96: "getpriority",
    97: "setpriority",
    99: "statfs",
    100: "fstatfs",
    101: "ioperm",
    102: "socketcall",
    103: "syslog",
    104: "setitimer",
    105: "getitimer",
    106: "stat",
    107: "lstat",
    108: "fstat",
    109: "olduname",
    110: "iopl",
    111: "vhangup",
    112: "idle",
    113: "vm86old",
    114: "wait4",
    115: "swapoff",
    116: "sysinfo",
    117: "ipc",
    118: "fsync",
    119: "sigreturn",
    120: "clone",
    121: "setdomainname",
    122: "uname",
    123: "modify_ldt",
    124: "adjtimex",
    125: "mprotect",
    126: "sigprocmask",
    127: "create_module",
    128: "init_module",
    129: "delete_module",
    130: "get_kernel_syms",
    131: "quotactl",
    132: "getpgid",
    133: "fchdir",
    134: "bdflush",
    135: "sysfs",
    136: "personality",
    138: "setfsuid",
    139: "setfsgid",
    140: "_llseek",
    141: "getdents",
    142: "_newselect",
    143: "flock",
    144: "msync",
    145: "readv",
    146: "writev",
    147: "getsid",
    148: "fdatasync",
    149: "_sysctl",
    150: "mlock",
    151: "munlock",
    152: "mlockall",
    153: "munlockall",
    154: "sched_setparam",
    155: "sched_getparam",
    156: "sched_setscheduler",
    157: "sched_getscheduler",
    158: "sched_yield",
    159: "sched_get_priority_max",
    160: "sched_get_priority_min",
    161: "sched_rr_get_interval",
    162: "nanosleep",
    163: "mremap",
    164: "setresuid",
    165: "getresuid",
    166: "vm86",
    167: "query_module",
    168: "poll",
    169: "nfsservctl",
    170: "setresgid",
    171: "getresgid",
    172: "prctl",
    173: "rt_sigreturn",
    174: "rt_sigaction",
    175: "rt_sigprocmask",
    176: "rt_sigpending",
    177: "rt_sigtimedwait",
    178: "rt_sigqueueinfo",
    179: "rt_sigsuspend",
    180: "pread64",
    181: "pwrite64",
    182: "chown",
    183: "getcwd",
    184: "capget",
    185: "capset",
    186: "sigaltstack",
    187: "sendfile",
    188: "getpmsg",
    190: "vfork",
    191: "ugetrlimit",
    192: "mmap2",
    193: "truncate64",
    194: "ftruncate64",
    195: "stat64",
    196: "lstat64",
    197: "fstat64",
    198: "lchown32",
    199: "getuid32",
    200: "getgid32",
    201: "geteuid32",
    202: "getegid32",
    203: "setreuid32",
    204: "setregid32",
    205: "getgroups32",
    206: "setgroups32",
    207: "fchown32",
    208: "setresuid32",
    209: "getresuid32",
    210: "setresgid32",
    211: "getresgid32",
    212: "chown32",
    213: "setuid32",
    214: "setgid32",
    215: "setfsuid32",
    216: "setfsgid32",
    217: "pivot_root",
    218: "mincore",
    219: "madvise",
    220: "getdents64",
    221: "fcntl64",
    224: "gettid",
    225: "readahead",
    226: "setxattr",
    227: "lsetxattr",
    228: "fsetxattr",
    229: "getxattr",
    230: "lgetxattr",
    231: "fgetxattr",
    232: "listxattr",
    233: "llistxattr",
    234: "flistxattr",
    235: "removexattr",
    236: "lremovexattr",
    237: "fremovexattr",
    238: "tkill",
    239: "sendfile64",
    240: "futex",
    241: "sched_setaffinity",
    242: "sched_getaffinity",
    243: "set_thread_area",
    244: "get_thread_area",
    245: "io_setup",
    246: "io_destroy",
    247: "io_getevents",
    248: "io_submit",
    249: "io_cancel",
    250: "fadvise64",
    252: "exit_group",
    253: "lookup_dcookie",
    254: "epoll_create",
    255: "epoll_ctl",
    256: "epoll_wait",
    257: "remap_file_pages",
    258: "set_tid_address",
    259: "timer_create",
    260: "timer_settime",
    261: "timer_gettime",
    262: "timer_getoverrun",
    263: "timer_delete",
    264: "clock_settime",
    265: "clock_gettime",
    266: "clock_getres",
    267: "clock_nanosleep",
    268: "statfs64",
    269: "fstatfs64",
    270: "tgkill",
    271: "utimes",
    272: "fadvise64_64",
    274: "mbind",
    275: "get_mempolicy",
    276: "set_mempolicy",
    277: "mq_open",
    278: "mq_unlink",
    279: "mq_timedsend",
    280: "mq_timedreceive",
    281: "mq_notify",
    282: "mq_getsetattr",
    283: "kexec_load",
    284: "waitid",
    286: "add_key",
    287: "request_key",
    288: "keyctl",
    289: "ioprio_set",
    290: "ioprio_get",
    291: "inotify_init",
    292: "inotify_add_watch",
    293: "inotify_rm_watch",
    294: "migrate_pages",
    295: "openat",
    296: "mkdirat",
    297: "mknodat",
    298: "fchownat",
    299: "futimesat",
    300: "fstatat64",
    301: "unlinkat",
    302: "renameat",
    303: "linkat",
    304: "symlinkat",
    305: "readlinkat",
    306: "fchmodat",
    307: "faccessat",
    308: "pselect6",
    309: "ppoll",
    310: "unshare",
    311: "set_robust_list",
    312: "get_robust_list",
    313: "splice",
    314: "sync_file_range",
    315: "tee",
    316: "vmsplice",
    317: "move_pages",
    318: "getcpu",
    319: "epoll_pwait",
    320: "utimensat",
    321: "signalfd",
    322: "timerfd_create",
    323: "eventfd",
    324: "fallocate",
    325: "timerfd_settime",
    326: "timerfd_gettime",
    327: "signalfd4",
    328: "eventfd2",
    329: "epoll_create1",
    330: "dup3",
    331: "pipe2",
    332: "inotify_init1",
    333: "preadv",
    334: "pwritev",
    335: "rt_tgsigqueueinfo",
    336: "perf_event_open",
    337: "recvmmsg",
    338: "fanotify_init",
    339: "fanotify_mark",
    340: "prlimit64",
    341: "name_to_handle_at",
    342: "open_by_handle_at",
    343: "clock_adjtime",
    344: "syncfs",
    345: "sendmmsg",
    346: "setns",
    347: "process_vm_readv",
    348: "process_vm_writev",
    349: "kcmp",
    350: "finit_module",
    351: "sched_setattr",
    352: "sched_getattr",
    353: "renameat2",
    354: "seccomp",
    355: "getrandom",
    356: "memfd_create",
    357: "bpf",
    358: "execveat",
    359: "socket",
    360: "socketpair",
    361: "bind",
    362: "connect",
    363: "listen",
    364: "accept4",
    365: "getsockopt",
    366: "setsockopt",
    367: "getsockname",
    368: "getpeername",
    369: "sendto",
    370: "sendmsg",
    371: "recvfrom",
    372: "recvmsg",
    373: "shutdown",
    374: "userfaultfd",
    375: "membarrier",
    376: "mlock2",
    377: "copy_file_range",
    378: "preadv2",
    379: "pwritev2",
    380: "pkey_mprotect",
    381: "pkey_alloc",
    382: "pkey_free",
    383: "statx",
    384: "arch_prctl",
    385: "io_pgetevents",
    386: "rseq",
    393: "semget",
    394: "semctl",
    395: "shmget",
    396: "shmctl",
    397: "shmat",
    398: "shmdt",
    399: "msgget",
    400: "msgsnd",
    401: "msgrcv",
    402: "msgctl",
    403: "clock_gettime64",
    404: "clock_settime64",
    405: "clock_adjtime64",
    406: "clock_getres_time64",
    407: "clock_nanosleep_time64",
    408: "timer_gettime64",
    409: "timer_settime64",
    410: "timerfd_gettime64",
    411: "timerfd_settime64",
    412: "utimensat_time64",
    413: "pselect6_time64",
    414: "ppoll_time64",
    416: "io_pgetevents_time64",
    417: "recvmmsg_time64",
    418: "mq_timedsend_time64",
    419: "mq_timedreceive_time64",
    420: "semtimedop_time64",
    421: "rt_sigtimedwait_time64",
    422: "futex_time64",
    423: "sched_rr_get_interval_time64",
    424: "pidfd_send_signal",
    425: "io_uring_setup",
    426: "io_uring_enter",
    427: "io_uring_register",
    428: "open_tree",
    429: "move_mount",
    430: "fsopen",
    431: "fsconfig",
    432: "fsmount",
    433: "fspick",
    434: "pidfd_open",
    435: "clone3",
    436: "close_range",
    437: "openat2",
    438: "pidfd_getfd",
    439: "faccessat2",
    440: "process_madvise",
    441: "epoll_pwait2",
    442: "mount_setattr",
    443: "quotactl_fd",
    444: "landlock_create_ruleset",
    445: "landlock_add_rule",
    446: "landlock_restrict_self",
    447: "memfd_secret",
    448: "process_mrelease",
}

x8664_syscall_table = {
    0: "read",
    1: "write",
    2: "open",
    3: "close",
    4: "stat",
    5: "fstat",
    6: "lstat",
    7: "poll",
    8: "lseek",
    9: "mmap",
    10: "mprotect",
    11: "munmap",
    12: "brk",
    13: "rt_sigaction",
    14: "rt_sigprocmask",
    15: "rt_sigreturn",
    16: "ioctl",
    17: "pread64",
    18: "pwrite64",
    19: "readv",
    20: "writev",
    21: "access",
    22: "pipe",
    23: "select",
    24: "sched_yield",
    25: "mremap",
    26: "msync",
    27: "mincore",
    28: "madvise",
    29: "shmget",
    30: "shmat",
    31: "shmctl",
    32: "dup",
    33: "dup2",
    34: "pause",
    35: "nanosleep",
    36: "getitimer",
    37: "alarm",
    38: "setitimer",
    39: "getpid",
    40: "sendfile",
    41: "socket",
    42: "connect",
    43: "accept",
    44: "sendto",
    45: "recvfrom",
    46: "sendmsg",
    47: "recvmsg",
    48: "shutdown",
    49: "bind",
    50: "listen",
    51: "getsockname",
    52: "getpeername",
    53: "socketpair",
    54: "setsockopt",
    55: "getsockopt",
    56: "clone",
    57: "fork",
    58: "vfork",
    59: "execve",
    60: "exit",
    61: "wait4",
    62: "kill",
    63: "uname",
    64: "semget",
    65: "semop",
    66: "semctl",
    67: "shmdt",
    68: "msgget",
    69: "msgsnd",
    70: "msgrcv",
    71: "msgctl",
    72: "fcntl",
    73: "flock",
    74: "fsync",
    75: "fdatasync",
    76: "truncate",
    77: "ftruncate",
    78: "getdents",
    79: "getcwd",
    80: "chdir",
    81: "fchdir",
    82: "rename",
    83: "mkdir",
    84: "rmdir",
    85: "creat",
    86: "link",
    87: "unlink",
    88: "symlink",
    89: "readlink",
    90: "chmod",
    91: "fchmod",
    92: "chown",
    93: "fchown",
    94: "lchown",
    95: "umask",
    96: "gettimeofday",
    97: "getrlimit",
    98: "getrusage",
    99: "sysinfo",
    100: "times",
    101: "ptrace",
    102: "getuid",
    103: "syslog",
    104: "getgid",
    105: "setuid",
    106: "setgid",
    107: "geteuid",
    108: "getegid",
    109: "setpgid",
    110: "getppid",
    111: "getpgrp",
    112: "setsid",
    113: "setreuid",
    114: "setregid",
    115: "getgroups",
    116: "setgroups",
    117: "setresuid",
    118: "getresuid",
    119: "setresgid",
    120: "getresgid",
    121: "getpgid",
    122: "setfsuid",
    123: "setfsgid",
    124: "getsid",
    125: "capget",
    126: "capset",
    127: "rt_sigpending",
    128: "rt_sigtimedwait",
    129: "rt_sigqueueinfo",
    130: "rt_sigsuspend",
    131: "sigaltstack",
    132: "utime",
    133: "mknod",
    134: "uselib",
    135: "personality",
    136: "ustat",
    137: "statfs",
    138: "fstatfs",
    139: "sysfs",
    140: "getpriority",
    141: "setpriority",
    142: "sched_setparam",
    143: "sched_getparam",
    144: "sched_setscheduler",
    145: "sched_getscheduler",
    146: "sched_get_priority_max",
    147: "sched_get_priority_min",
    148: "sched_rr_get_interval",
    149: "mlock",
    150: "munlock",
    151: "mlockall",
    152: "munlockall",
    153: "vhangup",
    154: "modify_ldt",
    155: "pivot_root",
    156: "_sysctl",
    157: "prctl",
    158: "arch_prctl",
    159: "adjtimex",
    160: "setrlimit",
    161: "chroot",
    162: "sync",
    163: "acct",
    164: "settimeofday",
    165: "mount",
    166: "umount2",
    167: "swapon",
    168: "swapoff",
    169: "reboot",
    170: "sethostname",
    171: "setdomainname",
    172: "iopl",
    173: "ioperm",
    174: "create_module",
    175: "init_module",
    176: "delete_module",
    177: "get_kernel_syms",
    178: "query_module",
    179: "quotactl",
    180: "nfsservctl",
    181: "getpmsg",
    186: "gettid",
    187: "readahead",
    188: "setxattr",
    189: "lsetxattr",
    190: "fsetxattr",
    191: "getxattr",
    192: "lgetxattr",
    193: "fgetxattr",
    194: "listxattr",
    195: "llistxattr",
    196: "flistxattr",
    197: "removexattr",
    198: "lremovexattr",
    199: "fremovexattr",
    200: "tkill",
    201: "time",
    202: "futex",
    203: "sched_setaffinity",
    204: "sched_getaffinity",
    205: "set_thread_area",
    206: "io_setup",
    207: "io_destroy",
    208: "io_getevents",
    209: "io_submit",
    210: "io_cancel",
    211: "get_thread_area",
    212: "lookup_dcookie",
    213: "epoll_create",
    214: "epoll_ctl_old",
    215: "epoll_wait_old",
    216: "remap_file_pages",
    217: "getdents64",
    218: "set_tid_address",
    219: "restart_syscall",
    220: "semtimedop",
    221: "fadvise64",
    222: "timer_create",
    223: "timer_settime",
    224: "timer_gettime",
    225: "timer_getoverrun",
    226: "timer_delete",
    227: "clock_settime",
    228: "clock_gettime",
    229: "clock_getres",
    230: "clock_nanosleep",
    231: "exit_group",
    232: "epoll_wait",
    233: "epoll_ctl",
    234: "tgkill",
    235: "utimes",
    237: "mbind",
    238: "set_mempolicy",
    239: "get_mempolicy",
    240: "mq_open",
    241: "mq_unlink",
    242: "mq_timedsend",
    243: "mq_timedreceive",
    244: "mq_notify",
    245: "mq_getsetattr",
    246: "kexec_load",
    247: "waitid",
    248: "add_key",
    249: "request_key",
    250: "keyctl",
    251: "ioprio_set",
    252: "ioprio_get",
    253: "inotify_init",
    254: "inotify_add_watch",
    255: "inotify_rm_watch",
    256: "migrate_pages",
    257: "openat",
    258: "mkdirat",
    259: "mknodat",
    260: "fchownat",
    261: "futimesat",
    262: "newfstatat",
    263: "unlinkat",
    264: "renameat",
    265: "linkat",
    266: "symlinkat",
    267: "readlinkat",
    268: "fchmodat",
    269: "faccessat",
    270: "pselect6",
    271: "ppoll",
    272: "unshare",
    273: "set_robust_list",
    274: "get_robust_list",
    275: "splice",
    276: "tee",
    277: "sync_file_range",
    278: "vmsplice",
    279: "move_pages",
    280: "utimensat",
    281: "epoll_pwait",
    282: "signalfd",
    283: "timerfd_create",
    284: "eventfd",
    285: "fallocate",
    286: "timerfd_settime",
    287: "timerfd_gettime",
    288: "accept4",
    289: "signalfd4",
    290: "eventfd2",
    291: "epoll_create1",
    292: "dup3",
    293: "pipe2",
    294: "inotify_init1",
    295: "preadv",
    296: "pwritev",
    297: "rt_tgsigqueueinfo",
    298: "perf_event_open",
    299: "recvmmsg",
    300: "fanotify_init",
    301: "fanotify_mark",
    302: "prlimit64",
    303: "name_to_handle_at",
    304: "open_by_handle_at",
    305: "clock_adjtime",
    306: "syncfs",
    307: "sendmmsg",
    308: "setns",
    309: "getcpu",
    310: "process_vm_readv",
    311: "process_vm_writev",
    312: "kcmp",
    313: "finit_module",
    314: "sched_setattr",
    315: "sched_getattr",
    316: "renameat2",
    317: "seccomp",
    318: "getrandom",
    319: "memfd_create",
    320: "kexec_file_load",
    321: "bpf",
    322: "execveat",
    323: "userfaultfd",
    324: "membarrier",
    325: "mlock2",
    326: "copy_file_range",
    327: "preadv2",
    328: "pwritev2",
    329: "pkey_mprotect",
    330: "pkey_alloc",
    331: "pkey_free",
    332: "statx",
    333: "io_pgetevents",
    334: "rseq",
    424: "pidfd_send_signal",
    425: "io_uring_setup",
    426: "io_uring_enter",
    427: "io_uring_register",
    428: "open_tree",
    429: "move_mount",
    430: "fsopen",
    431: "fsconfig",
    432: "fsmount",
    433: "fspick",
    434: "pidfd_open",
    435: "clone3",
    436: "close_range",
    437: "openat2",
    438: "pidfd_getfd",
    439: "faccessat2",
    440: "process_madvise",
    441: "epoll_pwait2",
    442: "mount_setattr",
    443: "quotactl_fd",
    444: "landlock_create_ruleset",
    445: "landlock_add_rule",
    446: "landlock_restrict_self",
    447: "memfd_secret",
    448: "process_mrelease",
}

mips_syscall_table = {
    4000: "syscall",
    4001: "exit",
    4002: "fork",
    4003: "read",
    4004: "write",
    4005: "open",
    4006: "close",
    4007: "waitpid",
    4008: "creat",
    4009: "link",
    4010: "unlink",
    4011: "execve",
    4012: "chdir",
    4013: "time",
    4014: "mknod",
    4015: "chmod",
    4016: "lchown",
    4019: "lseek",
    4020: "getpid",
    4021: "mount",
    4022: "umount",
    4023: "setuid",
    4024: "getuid",
    4025: "stime",
    4026: "ptrace",
    4027: "alarm",
    4029: "pause",
    4030: "utime",
    4033: "access",
    4034: "nice",
    4036: "sync",
    4037: "kill",
    4038: "rename",
    4039: "mkdir",
    4040: "rmdir",
    4041: "dup",
    4042: "pipe",
    4043: "times",
    4045: "brk",
    4046: "setgid",
    4047: "getgid",
    4048: "signal",
    4049: "geteuid",
    4050: "getegid",
    4051: "acct",
    4052: "umount2",
    4054: "ioctl",
    4055: "fcntl",
    4057: "setpgid",
    4060: "umask",
    4061: "chroot",
    4062: "ustat",
    4063: "dup2",
    4064: "getppid",
    4065: "getpgrp",
    4066: "setsid",
    4067: "sigaction",
    4068: "sgetmask",
    4069: "ssetmask",
    4070: "setreuid",
    4071: "setregid",
    4072: "sigsuspend",
    4073: "sigpending",
    4074: "sethostname",
    4075: "setrlimit",
    4076: "getrlimit",
    4077: "getrusage",
    4078: "gettimeofday",
    4079: "settimeofday",
    4080: "getgroups",
    4081: "setgroups",
    4083: "symlink",
    4085: "readlink",
    4086: "uselib",
    4087: "swapon",
    4088: "reboot",
    4089: "readdir",
    4090: "mmap",
    4091: "munmap",
    4092: "truncate",
    4093: "ftruncate",
    4094: "fchmod",
    4095: "fchown",
    4096: "getpriority",
    4097: "setpriority",
    4099: "statfs",
    4100: "fstatfs",
    4101: "ioperm",
    4102: "socketcall",
    4103: "syslog",
    4104: "setitimer",
    4105: "getitimer",
    4106: "stat",
    4107: "lstat",
    4108: "fstat",
    4110: "iopl",
    4111: "vhangup",
    4112: "idle",
    4113: "vm86",
    4114: "wait4",
    4115: "swapoff",
    4116: "sysinfo",
    4117: "ipc",
    4118: "fsync",
    4119: "sigreturn",
    4120: "clone",
    4121: "setdomainname",
    4122: "uname",
    4123: "modify_ldt",
    4124: "adjtimex",
    4125: "mprotect",
    4126: "sigprocmask",
    4127: "create_module",
    4128: "init_module",
    4129: "delete_module",
    4130: "get_kernel_syms",
    4131: "quotactl",
    4132: "getpgid",
    4133: "fchdir",
    4134: "bdflush",
    4135: "sysfs",
    4136: "personality",
    4138: "setfsuid",
    4139: "setfsgid",
    4140: "_llseek",
    4141: "getdents",
    4142: "_newselect",
    4143: "flock",
    4144: "msync",
    4145: "readv",
    4146: "writev",
    4147: "cacheflush",
    4148: "cachectl",
    4149: "sysmips",
    4151: "getsid",
    4152: "fdatasync",
    4153: "_sysctl",
    4154: "mlock",
    4155: "munlock",
    4156: "mlockall",
    4157: "munlockall",
    4158: "sched_setparam",
    4159: "sched_getparam",
    4160: "sched_setscheduler",
    4161: "sched_getscheduler",
    4162: "sched_yield",
    4163: "sched_get_priority_max",
    4164: "sched_get_priority_min",
    4165: "sched_rr_get_interval",
    4166: "nanosleep",
    4167: "mremap",
    4168: "accept",
    4169: "bind",
    4170: "connect",
    4171: "getpeername",
    4172: "getsockname",
    4173: "getsockopt",
    4174: "listen",
    4175: "recv",
    4176: "recvfrom",
    4177: "recvmsg",
    4178: "send",
    4179: "sendmsg",
    4180: "sendto",
    4181: "setsockopt",
    4182: "shutdown",
    4183: "socket",
    4184: "socketpair",
    4185: "setresuid",
    4186: "getresuid",
    4187: "query_module",
    4188: "poll",
    4189: "nfsservctl",
    4190: "setresgid",
    4191: "getresgid",
    4192: "prctl",
    4193: "rt_sigreturn",
    4194: "rt_sigaction",
    4195: "rt_sigprocmask",
    4196: "rt_sigpending",
    4197: "rt_sigtimedwait",
    4198: "rt_sigqueueinfo",
    4199: "rt_sigsuspend",
    4200: "pread64",
    4201: "pwrite64",
    4202: "chown",
    4203: "getcwd",
    4204: "capget",
    4205: "capset",
    4206: "sigaltstack",
    4207: "sendfile",
    4208: "getpmsg",
    4210: "mmap2",
    4211: "truncate64",
    4212: "ftruncate64",
    4213: "stat64",
    4214: "lstat64",
    4215: "fstat64",
    4216: "pivot_root",
    4217: "mincore",
    4218: "madvise",
    4219: "getdents64",
    4220: "fcntl64",
    4222: "gettid",
    4223: "readahead",
    4224: "setxattr",
    4225: "lsetxattr",
    4226: "fsetxattr",
    4227: "getxattr",
    4228: "lgetxattr",
    4229: "fgetxattr",
    4230: "listxattr",
    4231: "llistxattr",
    4232: "flistxattr",
    4233: "removexattr",
    4234: "lremovexattr",
    4235: "fremovexattr",
    4236: "tkill",
    4237: "sendfile64",
    4238: "futex",
    4239: "sched_setaffinity",
    4240: "sched_getaffinity",
    4241: "io_setup",
    4242: "io_destroy",
    4243: "io_getevents",
    4244: "io_submit",
    4245: "io_cancel",
    4246: "exit_group",
    4247: "lookup_dcookie",
    4248: "epoll_create",
    4249: "epoll_ctl",
    4250: "epoll_wait",
    4251: "remap_file_pages",
    4252: "set_tid_address",
    4253: "restart_syscall",
    4254: "fadvise64",
    4255: "statfs64",
    4256: "fstatfs64",
    4257: "timer_create",
    4258: "timer_settime",
    4259: "timer_gettime",
    4260: "timer_getoverrun",
    4261: "timer_delete",
    4262: "clock_settime",
    4263: "clock_gettime",
    4264: "clock_getres",
    4265: "clock_nanosleep",
    4266: "tgkill",
    4267: "utimes",
    4268: "mbind",
    4269: "get_mempolicy",
    4270: "set_mempolicy",
    4271: "mq_open",
    4272: "mq_unlink",
    4273: "mq_timedsend",
    4274: "mq_timedreceive",
    4275: "mq_notify",
    4276: "mq_getsetattr",
    4278: "waitid",
    4280: "add_key",
    4281: "request_key",
    4282: "keyctl",
    4283: "set_thread_area",
    4284: "inotify_init",
    4285: "inotify_add_watch",
    4286: "inotify_rm_watch",
    4287: "migrate_pages",
    4288: "openat",
    4289: "mkdirat",
    4290: "mknodat",
    4291: "fchownat",
    4292: "futimesat",
    4293: "fstatat64",
    4294: "unlinkat",
    4295: "renameat",
    4296: "linkat",
    4297: "symlinkat",
    4298: "readlinkat",
    4299: "fchmodat",
    4300: "faccessat",
    4301: "pselect6",
    4302: "ppoll",
    4303: "unshare",
    4304: "splice",
    4305: "sync_file_range",
    4306: "tee",
    4307: "vmsplice",
    4308: "move_pages",
    4309: "set_robust_list",
    4310: "get_robust_list",
    4311: "kexec_load",
    4312: "getcpu",
    4313: "epoll_pwait",
    4314: "ioprio_set",
    4315: "ioprio_get",
    4316: "utimensat",
    4317: "signalfd",
    4318: "timerfd",
    4319: "eventfd",
    4320: "fallocate",
    4321: "timerfd_create",
    4322: "timerfd_gettime",
    4323: "timerfd_settime",
    4324: "signalfd4",
    4325: "eventfd2",
    4326: "epoll_create1",
    4327: "dup3",
    4328: "pipe2",
    4329: "inotify_init1",
    4330: "preadv",
    4331: "pwritev",
    4332: "rt_tgsigqueueinfo",
    4333: "perf_event_open",
    4334: "accept4",
    4335: "recvmmsg",
    4336: "fanotify_init",
    4337: "fanotify_mark",
    4338: "prlimit64",
    4339: "name_to_handle_at",
    4340: "open_by_handle_at",
    4341: "clock_adjtime",
    4342: "syncfs",
    4343: "sendmmsg",
    4344: "setns",
    4345: "process_vm_readv",
    4346: "process_vm_writev",
    4347: "kcmp",
    4348: "finit_module",
    4349: "sched_setattr",
    4350: "sched_getattr",
    4351: "renameat2",
    4352: "seccomp",
    4353: "getrandom",
    4354: "memfd_create",
    4355: "bpf",
    4356: "execveat",
    4357: "userfaultfd",
    4358: "membarrier",
    4359: "mlock2",
    4360: "copy_file_range",
    4361: "preadv2",
    4362: "pwritev2",
    4363: "pkey_mprotect",
    4364: "pkey_alloc",
    4365: "pkey_free",
    4366: "statx",
    4367: "rseq",
    4368: "io_pgetevents",
    4393: "semget",
    4394: "semctl",
    4395: "shmget",
    4396: "shmctl",
    4397: "shmat",
    4398: "shmdt",
    4399: "msgget",
    4400: "msgsnd",
    4401: "msgrcv",
    4402: "msgctl",
    4403: "clock_gettime64",
    4404: "clock_settime64",
    4405: "clock_adjtime64",
    4406: "clock_getres_time64",
    4407: "clock_nanosleep_time64",
    4408: "timer_gettime64",
    4409: "timer_settime64",
    4410: "timerfd_gettime64",
    4411: "timerfd_settime64",
    4412: "utimensat_time64",
    4413: "pselect6_time64",
    4414: "ppoll_time64",
    4416: "io_pgetevents_time64",
    4417: "recvmmsg_time64",
    4418: "mq_timedsend_time64",
    4419: "mq_timedreceive_time64",
    4420: "semtimedop_time64",
    4421: "rt_sigtimedwait_time64",
    4422: "futex_time64",
    4423: "sched_rr_get_interval_time64",
    4424: "pidfd_send_signal",
    4425: "io_uring_setup",
    4426: "io_uring_enter",
    4427: "io_uring_register",
    4428: "open_tree",
    4429: "move_mount",
    4430: "fsopen",
    4431: "fsconfig",
    4432: "fsmount",
    4433: "fspick",
    4434: "pidfd_open",
    4435: "clone3",
    4436: "close_range",
    4437: "openat2",
    4438: "pidfd_getfd",
    4439: "faccessat2",
    4440: "process_madvise",
    4441: "epoll_pwait2",
    4442: "mount_setattr",
    4443: "quotactl_fd",
    4444: "landlock_create_ruleset",
    4445: "landlock_add_rule",
    4446: "landlock_restrict_self",
    4448: "process_mrelease",
}

riscv32_syscall_table = {
    0: "io_setup",
    1: "io_destroy",
    2: "io_submit",
    3: "io_cancel",
    5: "setxattr",
    6: "lsetxattr",
    7: "fsetxattr",
    8: "getxattr",
    9: "lgetxattr",
    10: "fgetxattr",
    11: "listxattr",
    12: "llistxattr",
    13: "flistxattr",
    14: "removexattr",
    15: "lremovexattr",
    16: "fremovexattr",
    17: "getcwd",
    18: "lookup_dcookie",
    19: "eventfd2",
    20: "epoll_create1",
    21: "epoll_ctl",
    22: "epoll_pwait",
    23: "dup",
    24: "dup3",
    25: "fcntl",
    26: "inotify_init1",
    27: "inotify_add_watch",
    28: "inotify_rm_watch",
    29: "ioctl",
    30: "ioprio_set",
    31: "ioprio_get",
    32: "flock",
    33: "mknodat",
    34: "mkdirat",
    35: "unlinkat",
    36: "symlinkat",
    37: "linkat",
    38: "renameat",
    39: "umount2",
    40: "mount",
    41: "pivot_root",
    42: "nfsservctl",
    43: "statfs64",
    44: "fstatfs64",
    45: "truncate64",
    46: "ftruncate",
    47: "fallocate",
    48: "faccessat",
    49: "chdir",
    50: "fchdir",
    51: "chroot",
    52: "fchmod",
    53: "fchmodat",
    54: "fchownat",
    55: "fchown",
    56: "openat",
    57: "close",
    58: "vhangup",
    59: "pipe2",
    60: "quotactl",
    61: "getdents",
    62: "lseek",
    63: "read",
    64: "write",
    65: "readv",
    66: "writev",
    67: "pread",
    68: "pwrite",
    69: "preadv",
    70: "pwritev",
    71: "sendfile64",
    74: "signalfd4",
    75: "vmsplice",
    76: "splice",
    77: "tee",
    78: "readlinkat",
    79: "fstatat",
    80: "fstat",
    81: "sync",
    82: "fsync",
    83: "fdatasync",
    84: "sync_file_range",
    85: "timerfd_create",
    89: "acct",
    90: "capget",
    91: "capset",
    92: "personality",
    93: "exit",
    94: "exit_group",
    95: "waitid",
    96: "set_tid_address",
    97: "unshare",
    99: "set_robust_list",
    100: "get_robust_list",
    102: "getitimer",
    103: "setitimer",
    104: "kexec_load",
    105: "init_module",
    106: "delete_module",
    107: "timer_create",
    109: "timer_getoverrun",
    111: "timer_delete",
    113: "clock_gettime",
    116: "syslog",
    117: "ptrace",
    118: "sched_setparam",
    119: "sched_setscheduler",
    120: "sched_getscheduler",
    121: "sched_getparam",
    122: "sched_setaffinity",
    123: "sched_getaffinity",
    124: "sched_yield",
    125: "sched_get_priority_max",
    126: "sched_get_priority_min",
    128: "restart_syscall",
    129: "kill",
    130: "tkill",
    131: "tgkill",
    132: "sigaltstack",
    133: "rt_sigsuspend",
    134: "rt_sigaction",
    135: "rt_sigprocmask",
    136: "rt_sigpending",
    138: "rt_sigqueueinfo",
    139: "rt_sigreturn",
    140: "setpriority",
    141: "getpriority",
    142: "reboot",
    143: "setregid",
    144: "setgid",
    145: "setreuid",
    146: "setuid",
    147: "setresuid",
    148: "getresuid",
    149: "setresgid",
    150: "getresgid",
    151: "setfsuid",
    152: "setfsgid",
    153: "times",
    154: "setpgid",
    155: "getpgid",
    156: "getsid",
    157: "setsid",
    158: "getgroups",
    159: "setgroups",
    160: "uname",
    161: "sethostname",
    162: "setdomainname",
    163: "getrlimit",
    164: "setrlimit",
    165: "getrusage",
    166: "umask",
    167: "prctl",
    168: "getcpu",
    169: "gettimeofday",
    172: "getpid",
    173: "getppid",
    174: "getuid",
    175: "geteuid",
    176: "getgid",
    177: "getegid",
    178: "gettid",
    179: "sysinfo",
    180: "mq_open",
    181: "mq_unlink",
    184: "mq_notify",
    185: "mq_getsetattr",
    186: "msgget",
    187: "msgctl",
    188: "msgrcv",
    189: "msgsnd",
    190: "semget",
    191: "semctl",
    193: "semop",
    194: "shmget",
    195: "shmctl",
    196: "shmat",
    197: "shmdt",
    198: "socket",
    199: "socketpair",
    200: "bind",
    201: "listen",
    202: "accept",
    203: "connect",
    204: "getsockname",
    205: "getpeername",
    206: "sendto",
    207: "recvfrom",
    208: "setsockopt",
    209: "getsockopt",
    210: "shutdown",
    211: "sendmsg",
    212: "recvmsg",
    213: "readahead",
    214: "brk",
    215: "munmap",
    216: "mremap",
    217: "add_key",
    218: "request_key",
    219: "keyctl",
    220: "clone",
    221: "execve",
    222: "mmap2",
    223: "fadvise64_64",
    224: "swapon",
    225: "swapoff",
    226: "mprotect",
    227: "msync",
    228: "mlock",
    229: "munlock",
    230: "mlockall",
    231: "munlockall",
    232: "mincore",
    233: "madvise",
    234: "remap_file_pages",
    235: "mbind",
    236: "get_mempolicy",
    237: "set_mempolicy",
    238: "migrate_pages",
    239: "move_pages",
    240: "rt_tgsigqueueinfo",
    241: "perf_event_open",
    242: "accept4",
    259: "riscv_flush_icache",
    261: "prlimit64",
    262: "fanotify_init",
    263: "fanotify_mark",
    264: "name_to_handle_at",
    265: "open_by_handle_at",
    267: "syncfs",
    268: "setns",
    269: "sendmmsg",
    270: "process_vm_readv",
    271: "process_vm_writev",
    272: "kcmp",
    273: "finit_module",
    274: "sched_setattr",
    275: "sched_getattr",
    276: "renameat2",
    277: "seccomp",
    278: "getrandom",
    279: "memfd_create",
    280: "bpf",
    281: "execveat",
    282: "userfaultfd",
    283: "membarrier",
    284: "mlock2",
    285: "copy_file_range",
    286: "preadv2",
    287: "pwritev2",
    288: "pkey_mprotect",
    289: "pkey_alloc",
    290: "pkey_free",
    291: "statx",
    293: "rseq",
    294: "kexec_file_load",
    403: "clock_gettime64",
    404: "clock_settime64",
    405: "clock_adjtime64",
    406: "clock_getres_time64",
    407: "clock_nanosleep_time64",
    408: "timer_gettime64",
    409: "timer_settime64",
    410: "timerfd_gettime64",
    411: "timerfd_settime64",
    412: "utimensat_time64",
    413: "pselect6_time64",
    414: "ppoll_time64",
    416: "io_pgetevents_time64",
    417: "recvmmsg_time64",
    418: "mq_timedsend_time64",
    419: "mq_timedreceive_time64",
    420: "semtimedop_time64",
    421: "rt_sigtimedwait_time64",
    422: "futex_time64",
    423: "sched_rr_get_interval_time64",
    424: "pidfd_send_signal",
    425: "io_uring_setup",
    426: "io_uring_enter",
    427: "io_uring_register",
    428: "open_tree",
    429: "move_mount",
    430: "fsopen",
    431: "fsconfig",
    432: "fsmount",
    433: "fspick",
    434: "pidfd_open",
    436: "close_range",
    437: "openat2",
    438: "pidfd_getfd",
    439: "faccessat2",
    440: "process_madvise",
    441: "epoll_pwait2",
    442: "mount_setattr",
    443: "quotactl_fd",
    444: "landlock_create_ruleset",
    445: "landlock_add_rule",
    446: "landlock_restrict_self",
    448: "process_mrelease",
    1024: "open",
    1025: "link",
    1026: "unlink",
    1030: "mkdir",
    1033: "access",
    1038: "stat",
    1039: "lstat",
    1062: "time",
    2011: "getmainvars",
}

riscv64_syscall_table = {
    0: "io_setup",
    1: "io_destroy",
    2: "io_submit",
    3: "io_cancel",
    4: "io_getevents",
    5: "setxattr",
    6: "lsetxattr",
    7: "fsetxattr",
    8: "getxattr",
    9: "lgetxattr",
    10: "fgetxattr",
    11: "listxattr",
    12: "llistxattr",
    13: "flistxattr",
    14: "removexattr",
    15: "lremovexattr",
    16: "fremovexattr",
    17: "getcwd",
    18: "lookup_dcookie",
    19: "eventfd2",
    20: "epoll_create1",
    21: "epoll_ctl",
    22: "epoll_pwait",
    23: "dup",
    24: "dup3",
    25: "fcntl",
    26: "inotify_init1",
    27: "inotify_add_watch",
    28: "inotify_rm_watch",
    29: "ioctl",
    30: "ioprio_set",
    31: "ioprio_get",
    32: "flock",
    33: "mknodat",
    34: "mkdirat",
    35: "unlinkat",
    36: "symlinkat",
    37: "linkat",
    39: "umount2",
    40: "mount",
    41: "pivot_root",
    42: "nfsservctl",
    43: "statfs",
    44: "fstatfs",
    45: "truncate",
    46: "ftruncate",
    47: "fallocate",
    48: "faccessat",
    49: "chdir",
    50: "fchdir",
    51: "chroot",
    52: "fchmod",
    53: "fchmodat",
    54: "fchownat",
    55: "fchown",
    56: "openat",
    57: "close",
    58: "vhangup",
    59: "pipe2",
    60: "quotactl",
    61: "getdents64",
    62: "lseek",
    63: "read",
    64: "write",
    65: "readv",
    66: "writev",
    67: "pread64",
    68: "pwrite64",
    69: "preadv",
    70: "pwritev",
    71: "sendfile",
    72: "pselect6",
    73: "ppoll",
    74: "signalfd4",
    75: "vmsplice",
    76: "splice",
    77: "tee",
    78: "readlinkat",
    79: "newfstatat",
    80: "fstat",
    81: "sync",
    82: "fsync",
    83: "fdatasync",
    84: "sync_file_range",
    85: "timerfd_create",
    86: "timerfd_settime",
    87: "timerfd_gettime",
    88: "utimensat",
    89: "acct",
    90: "capget",
    91: "capset",
    92: "personality",
    93: "exit",
    94: "exit_group",
    95: "waitid",
    96: "set_tid_address",
    97: "unshare",
    98: "futex",
    99: "set_robust_list",
    100: "get_robust_list",
    101: "nanosleep",
    102: "getitimer",
    103: "setitimer",
    104: "kexec_load",
    105: "init_module",
    106: "delete_module",
    107: "timer_create",
    108: "timer_gettime",
    109: "timer_getoverrun",
    110: "timer_settime",
    111: "timer_delete",
    112: "clock_settime",
    113: "clock_gettime",
    114: "clock_getres",
    115: "clock_nanosleep",
    116: "syslog",
    117: "ptrace",
    118: "sched_setparam",
    119: "sched_setscheduler",
    120: "sched_getscheduler",
    121: "sched_getparam",
    122: "sched_setaffinity",
    123: "sched_getaffinity",
    124: "sched_yield",
    125: "sched_get_priority_max",
    126: "sched_get_priority_min",
    127: "sched_rr_get_interval",
    128: "restart_syscall",
    129: "kill",
    130: "tkill",
    131: "tgkill",
    132: "sigaltstack",
    133: "rt_sigsuspend",
    134: "rt_sigaction",
    135: "rt_sigprocmask",
    136: "rt_sigpending",
    137: "rt_sigtimedwait",
    138: "rt_sigqueueinfo",
    139: "rt_sigreturn",
    140: "setpriority",
    141: "getpriority",
    142: "reboot",
    143: "setregid",
    144: "setgid",
    145: "setreuid",
    146: "setuid",
    147: "setresuid",
    148: "getresuid",
    149: "setresgid",
    150: "getresgid",
    151: "setfsuid",
    152: "setfsgid",
    153: "times",
    154: "setpgid",
    155: "getpgid",
    156: "getsid",
    157: "setsid",
    158: "getgroups",
    159: "setgroups",
    160: "uname",
    161: "sethostname",
    162: "setdomainname",
    163: "getrlimit",
    164: "setrlimit",
    165: "getrusage",
    166: "umask",
    167: "prctl",
    168: "getcpu",
    169: "gettimeofday",
    170: "settimeofday",
    171: "adjtimex",
    172: "getpid",
    173: "getppid",
    174: "getuid",
    175: "geteuid",
    176: "getgid",
    177: "getegid",
    178: "gettid",
    179: "sysinfo",
    180: "mq_open",
    181: "mq_unlink",
    182: "mq_timedsend",
    183: "mq_timedreceive",
    184: "mq_notify",
    185: "mq_getsetattr",
    186: "msgget",
    187: "msgctl",
    188: "msgrcv",
    189: "msgsnd",
    190: "semget",
    191: "semctl",
    192: "semtimedop",
    193: "semop",
    194: "shmget",
    195: "shmctl",
    196: "shmat",
    197: "shmdt",
    198: "socket",
    199: "socketpair",
    200: "bind",
    201: "listen",
    202: "accept",
    203: "connect",
    204: "getsockname",
    205: "getpeername",
    206: "sendto",
    207: "recvfrom",
    208: "setsockopt",
    209: "getsockopt",
    210: "shutdown",
    211: "sendmsg",
    212: "recvmsg",
    213: "readahead",
    214: "brk",
    215: "munmap",
    216: "mremap",
    217: "add_key",
    218: "request_key",
    219: "keyctl",
    220: "clone",
    221: "execve",
    222: "mmap",
    223: "fadvise64",
    224: "swapon",
    225: "swapoff",
    226: "mprotect",
    227: "msync",
    228: "mlock",
    229: "munlock",
    230: "mlockall",
    231: "munlockall",
    232: "mincore",
    233: "madvise",
    234: "remap_file_pages",
    235: "mbind",
    236: "get_mempolicy",
    237: "set_mempolicy",
    238: "migrate_pages",
    239: "move_pages",
    240: "rt_tgsigqueueinfo",
    241: "perf_event_open",
    242: "accept4",
    243: "recvmmsg",
    259: "riscv_flush_icache",
    260: "wait4",
    261: "prlimit64",
    262: "fanotify_init",
    263: "fanotify_mark",
    264: "name_to_handle_at",
    265: "open_by_handle_at",
    266: "clock_adjtime",
    267: "syncfs",
    268: "setns",
    269: "sendmmsg",
    270: "process_vm_readv",
    271: "process_vm_writev",
    272: "kcmp",
    273: "finit_module",
    274: "sched_setattr",
    275: "sched_getattr",
    276: "renameat2",
    277: "seccomp",
    278: "getrandom",
    279: "memfd_create",
    280: "bpf",
    281: "execveat",
    282: "userfaultfd",
    283: "membarrier",
    284: "mlock2",
    285: "copy_file_range",
    286: "preadv2",
    287: "pwritev2",
    288: "pkey_mprotect",
    289: "pkey_alloc",
    290: "pkey_free",
    291: "statx",
    292: "io_pgetevents",
    293: "rseq",
    294: "kexec_file_load",
    424: "pidfd_send_signal",
    425: "io_uring_setup",
    426: "io_uring_enter",
    427: "io_uring_register",
    428: "open_tree",
    429: "move_mount",
    430: "fsopen",
    431: "fsconfig",
    432: "fsmount",
    433: "fspick",
    434: "pidfd_open",
    435: "clone3",
    436: "close_range",
    437: "openat2",
    438: "pidfd_getfd",
    439: "faccessat2",
    440: "process_madvise",
    441: "epoll_pwait2",
    442: "mount_setattr",
    443: "quotactl_fd",
    444: "landlock_create_ruleset",
    445: "landlock_add_rule",
    446: "landlock_restrict_self",
    448: "process_mrelease",
}

ppc_syscall_table = {
    0: "restart_syscall",
    1: "exit",
    2: "fork",
    3: "read",
    4: "write",
    5: "open",
    6: "close",
    7: "waitpid",
    8: "creat",
    9: "link",
    10: "unlink",
    11: "execve",
    12: "chdir",
    13: "time",
    14: "mknod",
    15: "chmod",
    16: "lchown",
    17: "break",
    18: "oldstat",
    19: "lseek",
    20: "getpid",
    21: "mount",
    22: "umount",
    23: "setuid",
    24: "getuid",
    25: "stime",
    26: "ptrace",
    27: "alarm",
    28: "oldfstat",
    29: "pause",
    30: "utime",
    31: "stty",
    32: "gtty",
    33: "access",
    34: "nice",
    35: "ftime",
    36: "sync",
    37: "kill",
    38: "rename",
    39: "mkdir",
    40: "rmdir",
    41: "dup",
    42: "pipe",
    43: "times",
    44: "prof",
    45: "brk",
    46: "setgid",
    47: "getgid",
    48: "signal",
    49: "geteuid",
    50: "getegid",
    51: "acct",
    52: "umount2",
    53: "lock",
    54: "ioctl",
    55: "fcntl",
    56: "mpx",
    57: "setpgid",
    58: "ulimit",
    59: "oldolduname",
    60: "umask",
    61: "chroot",
    62: "ustat",
    63: "dup2",
    64: "getppid",
    65: "getpgrp",
    66: "setsid",
    67: "sigaction",
    68: "sgetmask",
    69: "ssetmask",
    70: "setreuid",
    71: "setregid",
    72: "sigsuspend",
    73: "sigpending",
    74: "sethostname",
    75: "setrlimit",
    76: "getrlimit",
    77: "getrusage",
    78: "gettimeofday",
    79: "settimeofday",
    80: "getgroups",
    81: "setgroups",
    82: "select",
    83: "symlink",
    84: "oldlstat",
    85: "readlink",
    86: "uselib",
    87: "swapon",
    88: "reboot",
    89: "readdir",
    90: "mmap",
    91: "munmap",
    92: "truncate",
    93: "ftruncate",
    94: "fchmod",
    95: "fchown",
    96: "getpriority",
    97: "setpriority",
    98: "profil",
    99: "statfs",
    100: "fstatfs",
    101: "ioperm",
    102: "socketcall",
    103: "syslog",
    104: "setitimer",
    105: "getitimer",
    106: "stat",
    107: "lstat",
    108: "fstat",
    109: "olduname",
    110: "iopl",
    111: "vhangup",
    112: "idle",
    113: "vm86",
    114: "wait4",
    115: "swapoff",
    116: "sysinfo",
    117: "ipc",
    118: "fsync",
    119: "sigreturn",
    120: "clone",
    121: "setdomainname",
    122: "uname",
    123: "modify_ldt",
    124: "adjtimex",
    125: "mprotect",
    126: "sigprocmask",
    127: "create_module",
    128: "init_module",
    129: "delete_module",
    130: "get_kernel_syms",
    131: "quotactl",
    132: "getpgid",
    133: "fchdir",
    134: "bdflush",
    135: "sysfs",
    136: "personality",
    137: "afs_syscall",
    138: "setfsuid",
    139: "setfsgid",
    140: "_llseek",
    141: "getdents",
    142: "_newselect",
    143: "flock",
    144: "msync",
    145: "readv",
    146: "writev",
    147: "getsid",
    148: "fdatasync",
    149: "_sysctl",
    150: "mlock",
    151: "munlock",
    152: "mlockall",
    153: "munlockall",
    154: "sched_setparam",
    155: "sched_getparam",
    156: "sched_setscheduler",
    157: "sched_getscheduler",
    158: "sched_yield",
    159: "sched_get_priority_max",
    160: "sched_get_priority_min",
    161: "sched_rr_get_interval",
    162: "nanosleep",
    163: "mremap",
    164: "setresuid",
    165: "getresuid",
    166: "query_module",
    167: "poll",
    168: "nfsservctl",
    169: "setresgid",
    170: "getresgid",
    171: "prctl",
    172: "rt_sigreturn",
    173: "rt_sigaction",
    174: "rt_sigprocmask",
    175: "rt_sigpending",
    176: "rt_sigtimedwait",
    177: "rt_sigqueueinfo",
    178: "rt_sigsuspend",
    179: "pread64",
    180: "pwrite64",
    181: "chown",
    182: "getcwd",
    183: "capget",
    184: "capset",
    185: "sigaltstack",
    186: "sendfile",
    187: "getpmsg",
    188: "putpmsg",
    189: "vfork",
    190: "ugetrlimit",
    191: "readahead",
    192: "mmap2",
    193: "truncate64",
    194: "ftruncate64",
    195: "stat64",
    196: "lstat64",
    197: "fstat64",
    198: "pciconfig_read",
    199: "pciconfig_write",
    200: "pciconfig_iobase",
    201: "multiplexer",
    202: "getdents64",
    203: "pivot_root",
    204: "fcntl64",
    205: "madvise",
    206: "mincore",
    207: "gettid",
    208: "tkill",
    209: "setxattr",
    210: "lsetxattr",
    211: "fsetxattr",
    212: "getxattr",
    213: "lgetxattr",
    214: "fgetxattr",
    215: "listxattr",
    216: "llistxattr",
    217: "flistxattr",
    218: "removexattr",
    219: "lremovexattr",
    220: "fremovexattr",
    221: "futex",
    222: "sched_setaffinity",
    223: "sched_getaffinity",
    225: "tuxcall",
    226: "sendfile64",
    227: "io_setup",
    228: "io_destroy",
    229: "io_getevents",
    230: "io_submit",
    231: "io_cancel",
    232: "set_tid_address",
    233: "fadvise64",
    234: "exit_group",
    235: "lookup_dcookie",
    236: "epoll_create",
    237: "epoll_ctl",
    238: "epoll_wait",
    239: "remap_file_pages",
    240: "timer_create",
    241: "timer_settime",
    242: "timer_gettime",
    243: "timer_getoverrun",
    244: "timer_delete",
    245: "clock_settime",
    246: "clock_gettime",
    247: "clock_getres",
    248: "clock_nanosleep",
    249: "swapcontext",
    250: "tgkill",
    251: "utimes",
    252: "statfs64",
    253: "fstatfs64",
    254: "fadvise64_64",
    255: "rtas",
    256: "sys_debug_setcontext",
    258: "migrate_pages",
    259: "mbind",
    260: "get_mempolicy",
    261: "set_mempolicy",
    262: "mq_open",
    263: "mq_unlink",
    264: "mq_timedsend",
    265: "mq_timedreceive",
    266: "mq_notify",
    267: "mq_getsetattr",
    268: "kexec_load",
    269: "add_key",
    270: "request_key",
    271: "keyctl",
    272: "waitid",
    273: "ioprio_set",
    274: "ioprio_get",
    275: "inotify_init",
    276: "inotify_add_watch",
    277: "inotify_rm_watch",
    278: "spu_run",
    279: "spu_create",
    280: "pselect6",
    281: "ppoll",
    282: "unshare",
    283: "splice",
    284: "tee",
    285: "vmsplice",
    286: "openat",
    287: "mkdirat",
    288: "mknodat",
    289: "fchownat",
    290: "futimesat",
    291: "fstatat64",
    292: "unlinkat",
    293: "renameat",
    294: "linkat",
    295: "symlinkat",
    296: "readlinkat",
    297: "fchmodat",
    298: "faccessat",
    299: "get_robust_list",
    300: "set_robust_list",
    301: "move_pages",
    302: "getcpu",
    303: "epoll_pwait",
    304: "utimensat",
    305: "signalfd",
    306: "timerfd_create",
    307: "eventfd",
    308: "sync_file_range2",
    309: "fallocate",
    310: "subpage_prot",
    311: "timerfd_settime",
    312: "timerfd_gettime",
    313: "signalfd4",
    314: "eventfd2",
    315: "epoll_create1",
    316: "dup3",
    317: "pipe2",
    318: "inotify_init1",
    319: "perf_event_open",
    320: "preadv",
    321: "pwritev",
    322: "rt_tgsigqueueinfo",
    323: "fanotify_init",
    324: "fanotify_mark",
    325: "prlimit64",
    326: "socket",
    327: "bind",
    328: "connect",
    329: "listen",
    330: "accept",
    331: "getsockname",
    332: "getpeername",
    333: "socketpair",
    334: "send",
    335: "sendto",
    336: "recv",
    337: "recvfrom",
    338: "shutdown",
    339: "setsockopt",
    340: "getsockopt",
    341: "sendmsg",
    342: "recvmsg",
    343: "recvmmsg",
    344: "accept4",
    345: "name_to_handle_at",
    346: "open_by_handle_at",
    347: "clock_adjtime",
    348: "syncfs",
    349: "sendmmsg",
    350: "setns",
    351: "process_vm_readv",
    352: "process_vm_writev",
    353: "finit_module",
    354: "kcmp",
    355: "sched_setattr",
    356: "sched_getattr",
    357: "renameat2",
    358: "seccomp",
    359: "getrandom",
    360: "memfd_create",
    361: "bpf",
    362: "execveat",
    363: "switch_endian",
    364: "userfaultfd",
    365: "membarrier",
    378: "mlock2",
    379: "copy_file_range",
    380: "preadv2",
    381: "pwritev2",
    382: "kexec_file_load",
    383: "statx",
    384: "pkey_alloc",
    385: "pkey_free",
    386: "pkey_mprotect",
    387: "rseq",
    388: "io_pgetevents",
    393: "semget",
    394: "semctl",
    395: "shmget",
    396: "shmctl",
    397: "shmat",
    398: "shmdt",
    399: "msgget",
    400: "msgsnd",
    401: "msgrcv",
    402: "msgctl",
    403: "clock_gettime64",
    404: "clock_settime64",
    405: "clock_adjtime64",
    406: "clock_getres_time64",
    407: "clock_nanosleep_time64",
    408: "timer_gettime64",
    409: "timer_settime64",
    410: "timerfd_gettime64",
    411: "timerfd_settime64",
    412: "utimensat_time64",
    413: "pselect6_time64",
    414: "ppoll_time64",
    416: "io_pgetevents_time64",
    417: "recvmmsg_time64",
    418: "mq_timedsend_time64",
    419: "mq_timedreceive_time64",
    420: "semtimedop_time64",
    421: "rt_sigtimedwait_time64",
    422: "futex_time64",
    423: "sched_rr_get_interval_time64",
    424: "pidfd_send_signal",
    425: "io_uring_setup",
    426: "io_uring_enter",
    427: "io_uring_register",
    428: "open_tree",
    429: "move_mount",
    430: "fsopen",
    431: "fsconfig",
    432: "fsmount",
    433: "fspick",
}
