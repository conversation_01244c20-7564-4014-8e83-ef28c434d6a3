name: sync to gitee
on:
  push:

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.repository_owner == 'qilingframework'
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0
      - uses: xwings/sync-repo-action@master
        with:
          run: git config --global --add safe.directory *
          ssh_private_key: ${{ secrets.GITEE_KEY }}
          target_repo: ssh://*************/qilingframework/qiling.git
