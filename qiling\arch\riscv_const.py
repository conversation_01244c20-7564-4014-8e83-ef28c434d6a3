#!/usr/bin/env python3
#
# Cross Platform and Multi Architecture Advanced Binary Emulation Framework
#

from unicorn.riscv_const import *
from enum import IntEnum

reg_general_map = {
    "x0":  UC_RISCV_REG_X0,
    "x1":  UC_RISCV_REG_X1,
    "x2":  UC_RISCV_REG_X2,
    "x3":  UC_RISCV_REG_X3,
    "x4":  UC_RISCV_REG_X4,
    "x5":  UC_RISCV_REG_X5,
    "x6":  UC_RISCV_REG_X6,
    "x7":  UC_RISCV_REG_X7,
    "x8":  UC_RISCV_REG_X8,
    "x9":  UC_RISCV_REG_X9,
    "x10": UC_RISCV_REG_X10,
    "x11": UC_RISCV_REG_X11,
    "x12": UC_RISCV_REG_X12,
    "x13": UC_RISCV_REG_X13,
    "x14": UC_RISCV_REG_X14,
    "x15": UC_RISCV_REG_X15,
    "x16": UC_RISCV_REG_X16,
    "x17": UC_RISCV_REG_X17,
    "x18": UC_RISCV_REG_X18,
    "x19": UC_RISCV_REG_X19,
    "x20": UC_RISCV_REG_X20,
    "x21": UC_RISCV_REG_X21,
    "x22": UC_RISCV_REG_X22,
    "x23": UC_RISCV_REG_X23,
    "x24": UC_RISCV_REG_X24,
    "x25": UC_RISCV_REG_X25,
    "x26": UC_RISCV_REG_X26,
    "x27": UC_RISCV_REG_X27,
    "x28": UC_RISCV_REG_X28,
    "x29": UC_RISCV_REG_X29,
    "x30": UC_RISCV_REG_X30,
    "x31": UC_RISCV_REG_X31
}

reg_csr_map = {
    "ustatus":      UC_RISCV_REG_USTATUS,
    "uie":          UC_RISCV_REG_UIE,
    "utvec":        UC_RISCV_REG_UTVEC,
    "uscratch":     UC_RISCV_REG_USCRATCH,
    "uepc":         UC_RISCV_REG_UEPC,
    "ucause":       UC_RISCV_REG_UCAUSE,
    "utval":        UC_RISCV_REG_UTVAL,
    "uip":          UC_RISCV_REG_UIP,
    "fflags":       UC_RISCV_REG_FFLAGS,
    "frm":          UC_RISCV_REG_FRM,
    "fcsr":         UC_RISCV_REG_FCSR,
    "cycle":        UC_RISCV_REG_CYCLE,
    "time":         UC_RISCV_REG_TIME,
    "instret":      UC_RISCV_REG_INSTRET,
    "hpmcounter3":  UC_RISCV_REG_HPMCOUNTER3,
    "hpmcounter4":  UC_RISCV_REG_HPMCOUNTER4,
    "hpmcounter5":  UC_RISCV_REG_HPMCOUNTER5,
    "hpmcounter6":  UC_RISCV_REG_HPMCOUNTER6,
    "hpmcounter7":  UC_RISCV_REG_HPMCOUNTER7,
    "hpmcounter8":  UC_RISCV_REG_HPMCOUNTER8,
    "hpmcounter9":  UC_RISCV_REG_HPMCOUNTER9,
    "hpmcounter10": UC_RISCV_REG_HPMCOUNTER10,
    "hpmcounter11": UC_RISCV_REG_HPMCOUNTER11,
    "hpmcounter12": UC_RISCV_REG_HPMCOUNTER12,
    "hpmcounter13": UC_RISCV_REG_HPMCOUNTER13,
    "hpmcounter14": UC_RISCV_REG_HPMCOUNTER14,
    "hpmcounter15": UC_RISCV_REG_HPMCOUNTER15,
    "hpmcounter16": UC_RISCV_REG_HPMCOUNTER16,
    "hpmcounter17": UC_RISCV_REG_HPMCOUNTER17,
    "hpmcounter18": UC_RISCV_REG_HPMCOUNTER18,
    "hpmcounter19": UC_RISCV_REG_HPMCOUNTER19,
    "hpmcounter20": UC_RISCV_REG_HPMCOUNTER20,
    "hpmcounter21": UC_RISCV_REG_HPMCOUNTER21,
    "hpmcounter22": UC_RISCV_REG_HPMCOUNTER22,
    "hpmcounter23": UC_RISCV_REG_HPMCOUNTER23,
    "hpmcounter24": UC_RISCV_REG_HPMCOUNTER24,
    "hpmcounter25": UC_RISCV_REG_HPMCOUNTER25,
    "hpmcounter26": UC_RISCV_REG_HPMCOUNTER26,
    "hpmcounter27": UC_RISCV_REG_HPMCOUNTER27,
    "hpmcounter28": UC_RISCV_REG_HPMCOUNTER28,
    "hpmcounter29": UC_RISCV_REG_HPMCOUNTER29,
    "hpmcounter30": UC_RISCV_REG_HPMCOUNTER30,
    "hpmcounter31": UC_RISCV_REG_HPMCOUNTER31,
    "cycleh":       UC_RISCV_REG_CYCLEH,
    "timeh":        UC_RISCV_REG_TIMEH,
    "instreth":     UC_RISCV_REG_INSTRETH,
    "hpmcounter3h": UC_RISCV_REG_HPMCOUNTER3H,
    "hpmcounter4h": UC_RISCV_REG_HPMCOUNTER4H,
    "hpmcounter5h": UC_RISCV_REG_HPMCOUNTER5H,
    "hpmcounter6h": UC_RISCV_REG_HPMCOUNTER6H,
    "hpmcounter7h": UC_RISCV_REG_HPMCOUNTER7H,
    "hpmcounter8h": UC_RISCV_REG_HPMCOUNTER8H,
    "hpmcounter9h": UC_RISCV_REG_HPMCOUNTER9H,
    "hpmcounter10h": UC_RISCV_REG_HPMCOUNTER10H,
    "hpmcounter11h": UC_RISCV_REG_HPMCOUNTER11H,
    "hpmcounter12h": UC_RISCV_REG_HPMCOUNTER12H,
    "hpmcounter13h": UC_RISCV_REG_HPMCOUNTER13H,
    "hpmcounter14h": UC_RISCV_REG_HPMCOUNTER14H,
    "hpmcounter15h": UC_RISCV_REG_HPMCOUNTER15H,
    "hpmcounter16h": UC_RISCV_REG_HPMCOUNTER16H,
    "hpmcounter17h": UC_RISCV_REG_HPMCOUNTER17H,
    "hpmcounter18h": UC_RISCV_REG_HPMCOUNTER18H,
    "hpmcounter19h": UC_RISCV_REG_HPMCOUNTER19H,
    "hpmcounter20h": UC_RISCV_REG_HPMCOUNTER20H,
    "hpmcounter21h": UC_RISCV_REG_HPMCOUNTER21H,
    "hpmcounter22h": UC_RISCV_REG_HPMCOUNTER22H,
    "hpmcounter23h": UC_RISCV_REG_HPMCOUNTER23H,
    "hpmcounter24h": UC_RISCV_REG_HPMCOUNTER24H,
    "hpmcounter25h": UC_RISCV_REG_HPMCOUNTER25H,
    "hpmcounter26h": UC_RISCV_REG_HPMCOUNTER26H,
    "hpmcounter27h": UC_RISCV_REG_HPMCOUNTER27H,
    "hpmcounter28h": UC_RISCV_REG_HPMCOUNTER28H,
    "hpmcounter29h": UC_RISCV_REG_HPMCOUNTER29H,
    "hpmcounter30h": UC_RISCV_REG_HPMCOUNTER30H,
    "hpmcounter31h": UC_RISCV_REG_HPMCOUNTER31H,
    "mcycle":       UC_RISCV_REG_MCYCLE,
    "minstret":     UC_RISCV_REG_MINSTRET,
    "mcycleh":      UC_RISCV_REG_MCYCLEH,
    "minstreth":    UC_RISCV_REG_MINSTRETH,
    "mvendorid":    UC_RISCV_REG_MVENDORID,
    "marchid":      UC_RISCV_REG_MARCHID,
    "mimpid":       UC_RISCV_REG_MIMPID,
    "mhartid":      UC_RISCV_REG_MHARTID,
    "mstatus":      UC_RISCV_REG_MSTATUS,
    "misa":         UC_RISCV_REG_MISA,
    "medeleg":      UC_RISCV_REG_MEDELEG,
    "mideleg":      UC_RISCV_REG_MIDELEG,
    "mie":          UC_RISCV_REG_MIE,
    "mtvec":        UC_RISCV_REG_MTVEC,
    "mcounteren":   UC_RISCV_REG_MCOUNTEREN,
    "mstatush":     UC_RISCV_REG_MSTATUSH,
    "mucounteren":  UC_RISCV_REG_MUCOUNTEREN,
    "mscounteren":  UC_RISCV_REG_MSCOUNTEREN,
    "mhcounteren":  UC_RISCV_REG_MHCOUNTEREN,
    "mscratch":     UC_RISCV_REG_MSCRATCH,
    "mepc":         UC_RISCV_REG_MEPC,
    "mcause":       UC_RISCV_REG_MCAUSE,
    "mtval":        UC_RISCV_REG_MTVAL,
    "mip":          UC_RISCV_REG_MIP,
    "mbadaddr":     UC_RISCV_REG_MBADADDR,
    "sstatus":      UC_RISCV_REG_SSTATUS,
    "sedeleg":      UC_RISCV_REG_SEDELEG,
    "sideleg":      UC_RISCV_REG_SIDELEG,
    "sie":          UC_RISCV_REG_SIE,
    "stvec":        UC_RISCV_REG_STVEC,
    "scounteren":   UC_RISCV_REG_SCOUNTEREN,
    "sscratch":     UC_RISCV_REG_SSCRATCH,
    "sepc":         UC_RISCV_REG_SEPC,
    "scause":       UC_RISCV_REG_SCAUSE,
    "stval":        UC_RISCV_REG_STVAL,
    "sip":          UC_RISCV_REG_SIP,
    "sbadaddr":     UC_RISCV_REG_SBADADDR,
    "sptbr":        UC_RISCV_REG_SPTBR,
    "satp":         UC_RISCV_REG_SATP,
    "hstatus":      UC_RISCV_REG_HSTATUS,
    "hedeleg":      UC_RISCV_REG_HEDELEG,
    "hideleg":      UC_RISCV_REG_HIDELEG,
    "hie":          UC_RISCV_REG_HIE,
    "hcounteren":   UC_RISCV_REG_HCOUNTEREN,
    "htval":        UC_RISCV_REG_HTVAL,
    "hip":          UC_RISCV_REG_HIP,
    "htinst":       UC_RISCV_REG_HTINST,
    "hgatp":        UC_RISCV_REG_HGATP,
    "htimedelta":   UC_RISCV_REG_HTIMEDELTA,
    "htimedeltah": UC_RISCV_REG_HTIMEDELTAH
}

reg_float_map = {
    "f0":  UC_RISCV_REG_F0,
    "f1":  UC_RISCV_REG_F1,
    "f2":  UC_RISCV_REG_F2,
    "f3":  UC_RISCV_REG_F3,
    "f4":  UC_RISCV_REG_F4,
    "f5":  UC_RISCV_REG_F5,
    "f6":  UC_RISCV_REG_F6,
    "f7":  UC_RISCV_REG_F7,
    "f8":  UC_RISCV_REG_F8,
    "f9":  UC_RISCV_REG_F9,
    "f10": UC_RISCV_REG_F10,
    "f11": UC_RISCV_REG_F11,
    "f12": UC_RISCV_REG_F12,
    "f13": UC_RISCV_REG_F13,
    "f14": UC_RISCV_REG_F14,
    "f15": UC_RISCV_REG_F15,
    "f16": UC_RISCV_REG_F16,
    "f17": UC_RISCV_REG_F17,
    "f18": UC_RISCV_REG_F18,
    "f19": UC_RISCV_REG_F19,
    "f20": UC_RISCV_REG_F20,
    "f21": UC_RISCV_REG_F21,
    "f22": UC_RISCV_REG_F22,
    "f23": UC_RISCV_REG_F23,
    "f24": UC_RISCV_REG_F24,
    "f25": UC_RISCV_REG_F25,
    "f26": UC_RISCV_REG_F26,
    "f27": UC_RISCV_REG_F27,
    "f28": UC_RISCV_REG_F28,
    "f29": UC_RISCV_REG_F29,
    "f30": UC_RISCV_REG_F30,
    "f31": UC_RISCV_REG_F31
}

reg_map = {
    "zero": UC_RISCV_REG_ZERO,
    "ra":   UC_RISCV_REG_RA,
    "sp":   UC_RISCV_REG_SP,
    "gp":   UC_RISCV_REG_GP,
    "tp":   UC_RISCV_REG_TP,
    "t0":   UC_RISCV_REG_T0,
    "t1":   UC_RISCV_REG_T1,
    "t2":   UC_RISCV_REG_T2,
    "s0":   UC_RISCV_REG_S0,
    "fp":   UC_RISCV_REG_FP,
    "s1":   UC_RISCV_REG_S1,
    "a0":   UC_RISCV_REG_A0,
    "a1":   UC_RISCV_REG_A1,
    "a2":   UC_RISCV_REG_A2,
    "a3":   UC_RISCV_REG_A3,
    "a4":   UC_RISCV_REG_A4,
    "a5":   UC_RISCV_REG_A5,
    "a6":   UC_RISCV_REG_A6,
    "a7":   UC_RISCV_REG_A7,
    "s2":   UC_RISCV_REG_S2,
    "s3":   UC_RISCV_REG_S3,
    "s4":   UC_RISCV_REG_S4,
    "s5":   UC_RISCV_REG_S5,
    "s6":   UC_RISCV_REG_S6,
    "s7":   UC_RISCV_REG_S7,
    "s8":   UC_RISCV_REG_S8,
    "s9":   UC_RISCV_REG_S9,
    "s10":  UC_RISCV_REG_S10,
    "s11":  UC_RISCV_REG_S11,
    "t3":   UC_RISCV_REG_T3,
    "t4":   UC_RISCV_REG_T4,
    "t5":   UC_RISCV_REG_T5,
    "t6":   UC_RISCV_REG_T6,
    "ft0":  UC_RISCV_REG_FT0,
    "ft1":  UC_RISCV_REG_FT1,
    "ft2":  UC_RISCV_REG_FT2,
    "ft3":  UC_RISCV_REG_FT3,
    "ft4":  UC_RISCV_REG_FT4,
    "ft5":  UC_RISCV_REG_FT5,
    "ft6":  UC_RISCV_REG_FT6,
    "ft7":  UC_RISCV_REG_FT7,
    "fs0":  UC_RISCV_REG_FS0,
    "fs1":  UC_RISCV_REG_FS1,
    "fa0":  UC_RISCV_REG_FA0,
    "fa1":  UC_RISCV_REG_FA1,
    "fa2":  UC_RISCV_REG_FA2,
    "fa3":  UC_RISCV_REG_FA3,
    "fa4":  UC_RISCV_REG_FA4,
    "fa5":  UC_RISCV_REG_FA5,
    "fa6":  UC_RISCV_REG_FA6,
    "fa7":  UC_RISCV_REG_FA7,
    "fs2":  UC_RISCV_REG_FS2,
    "fs3":  UC_RISCV_REG_FS3,
    "fs4":  UC_RISCV_REG_FS4,
    "fs5":  UC_RISCV_REG_FS5,
    "fs6":  UC_RISCV_REG_FS6,
    "fs7":  UC_RISCV_REG_FS7,
    "fs8":  UC_RISCV_REG_FS8,
    "fs9":  UC_RISCV_REG_FS9,
    "fs10": UC_RISCV_REG_FS10,
    "fs11": UC_RISCV_REG_FS11,
    "ft8":  UC_RISCV_REG_FT8,
    "ft9":  UC_RISCV_REG_FT9,
    "ft10": UC_RISCV_REG_FT10,
    "ft11": UC_RISCV_REG_FT11,
    "pc":   UC_RISCV_REG_PC
}


class MSTATUS(IntEnum):
    FS_OFF = 0
    FS_INITIAL = 1 << 13
    FS_CLEAN   = 2 << 13
    FS_DIRTY   = 3 << 13
