#!/usr/bin/env python3
#
# Cross Platform and Multi Architecture Advanced Binary Emulation Framework
#

PROCESSOR_FEATURE_MAX = 64
MAX_WOW64_SHARED_ENTRIES = 16

# IRP Major Function Codes
IRP_MJ_CREATE = 0x00
IRP_MJ_CREATE_NAMED_PIPE = 0x01
IRP_MJ_CLOSE = 0x02
IRP_MJ_READ = 0x03
IRP_MJ_WRITE = 0x04
IRP_MJ_QUERY_INFORMATION = 0x05
IRP_MJ_SET_INFORMATION = 0x06
IRP_MJ_QUERY_EA = 0x07
IRP_MJ_SET_EA = 0x08
IRP_MJ_FLUSH_BUFFERS = 0x09
IRP_MJ_QUERY_VOLUME_INFORMATION = 0x0a
IRP_MJ_SET_VOLUME_INFORMATION = 0x0b
IRP_MJ_DIRECTORY_CONTROL = 0x0c
IRP_MJ_FILE_SYSTEM_CONTROL = 0x0d
IRP_MJ_DEVICE_CONTROL = 0x0e
IRP_MJ_INTERNAL_DEVICE_CONTROL = 0x0f
IRP_MJ_SHUTDOWN = 0x10
IRP_MJ_LOCK_CONTROL = 0x11
IRP_MJ_CLEANUP = 0x12
IRP_MJ_CREATE_MAILSLOT = 0x13
IRP_MJ_QUERY_SECURITY = 0x14
IRP_MJ_SET_SECURITY = 0x15
IRP_MJ_POWER = 0x16
IRP_MJ_SYSTEM_CONTROL = 0x17
IRP_MJ_DEVICE_CHANGE = 0x18
IRP_MJ_QUERY_QUOTA = 0x19
IRP_MJ_SET_QUOTA = 0x1a
IRP_MJ_PNP = 0x1b
IRP_MJ_MAXIMUM_FUNCTION = 0x1b

# Device Types
device_name_unknown =              0x00000000
FILE_DEVICE_BEEP =                 0x00000001
FILE_DEVICE_CD_ROM =               0x00000002
FILE_DEVICE_CD_ROM_FILE_SYSTEM =   0x00000003
FILE_DEVICE_CONTROLLER =           0x00000004
FILE_DEVICE_DATALINK =             0x00000005
FILE_DEVICE_DFS =                  0x00000006
FILE_DEVICE_DISK =                 0x00000007
FILE_DEVICE_DISK_FILE_SYSTEM =     0x00000008
FILE_DEVICE_FILE_SYSTEM =          0x00000009
FILE_DEVICE_INPORT_PORT =          0x0000000a
FILE_DEVICE_KEYBOARD =             0x0000000b
FILE_DEVICE_MAILSLOT =             0x0000000c
FILE_DEVICE_MIDI_IN =              0x0000000d
FILE_DEVICE_MIDI_OUT =             0x0000000e
FILE_DEVICE_MOUSE =                0x0000000f
FILE_DEVICE_MULTI_UNC_PROVIDER =   0x00000010
FILE_DEVICE_NAMED_PIPE =           0x00000011
FILE_DEVICE_NETWORK =              0x00000012
FILE_DEVICE_NETWORK_BROWSER =      0x00000013
FILE_DEVICE_NETWORK_FILE_SYSTEM =  0x00000014
FILE_DEVICE_NULL =                 0x00000015
FILE_DEVICE_PARALLEL_PORT =        0x00000016
FILE_DEVICE_PHYSICAL_NETCARD =     0x00000017
FILE_DEVICE_PRINTER =              0x00000018
FILE_DEVICE_SCANNER =              0x00000019
FILE_DEVICE_SERIAL_MOUSE_PORT =    0x0000001a
FILE_DEVICE_SERIAL_PORT =          0x0000001b
FILE_DEVICE_SCREEN =               0x0000001c
FILE_DEVICE_SOUND =                0x0000001d
FILE_DEVICE_STREAMS =              0x0000001e
FILE_DEVICE_TAPE =                 0x0000001f
FILE_DEVICE_TAPE_FILE_SYSTEM =     0x00000020
FILE_DEVICE_TRANSPORT =            0x00000021
FILE_DEVICE_UNKNOWN =              0x00000022
FILE_DEVICE_VIDEO =                0x00000023
FILE_DEVICE_VIRTUAL_DISK =         0x00000024
FILE_DEVICE_WAVE_IN =              0x00000025
FILE_DEVICE_WAVE_OUT =             0x00000026
FILE_DEVICE_8042_PORT =            0x00000027
FILE_DEVICE_NETWORK_REDIRECTOR =   0x00000028
FILE_DEVICE_BATTERY =              0x00000029
FILE_DEVICE_BUS_EXTENDER =         0x0000002a
FILE_DEVICE_MODEM =                0x0000002b
FILE_DEVICE_VDM =                  0x0000002c
FILE_DEVICE_MASS_STORAGE =         0x0000002d
FILE_DEVICE_SMB =                  0x0000002e
FILE_DEVICE_KS =                   0x0000002f
FILE_DEVICE_CHANGER =              0x00000030
FILE_DEVICE_SMARTCARD =            0x00000031
FILE_DEVICE_ACPI =                 0x00000032
FILE_DEVICE_DVD =                  0x00000033
FILE_DEVICE_FULLSCREEN_VIDEO =     0x00000034
FILE_DEVICE_DFS_FILE_SYSTEM =      0x00000035
FILE_DEVICE_DFS_VOLUME =           0x00000036
FILE_DEVICE_SERENUM =              0x00000037
FILE_DEVICE_TERMSRV =              0x00000038
FILE_DEVICE_KSEC =                 0x00000039
FILE_DEVICE_FIPS =                 0x0000003A
FILE_DEVICE_INFINIBAND =           0x0000003B
FILE_DEVICE_VMBUS =                0x0000003E
FILE_DEVICE_CRYPT_PROVIDER =       0x0000003F
FILE_DEVICE_WPD =                  0x00000040
FILE_DEVICE_BLUETOOTH =            0x00000041
FILE_DEVICE_MT_COMPOSITE =         0x00000042
FILE_DEVICE_MT_TRANSPORT =         0x00000043
FILE_DEVICE_BIOMETRIC =            0x00000044
FILE_DEVICE_PMI =                  0x00000045

DEVICE_TYPE = {
    device_name_unknown: "device_name_unknown",
    FILE_DEVICE_BEEP: "FILE_DEVICE_BEEP",
    FILE_DEVICE_CD_ROM: "FILE_DEVICE_CD_ROM",
    FILE_DEVICE_CD_ROM_FILE_SYSTEM: "FILE_DEVICE_CD_ROM_FILE_SYSTEM",
    FILE_DEVICE_CONTROLLER: "FILE_DEVICE_CONTROLLER",
    FILE_DEVICE_DATALINK: "FILE_DEVICE_DATALINK",
    FILE_DEVICE_DFS: "FILE_DEVICE_DFS",
    FILE_DEVICE_DISK: "FILE_DEVICE_DISK",
    FILE_DEVICE_DISK_FILE_SYSTEM: "FILE_DEVICE_DISK_FILE_SYSTEM",
    FILE_DEVICE_FILE_SYSTEM: "FILE_DEVICE_FILE_SYSTEM",
    FILE_DEVICE_INPORT_PORT: "FILE_DEVICE_INPORT_PORT",
    FILE_DEVICE_KEYBOARD: "FILE_DEVICE_KEYBOARD",
    FILE_DEVICE_MAILSLOT: "FILE_DEVICE_MAILSLOT",
    FILE_DEVICE_MIDI_IN: "FILE_DEVICE_MIDI_IN",
    FILE_DEVICE_MIDI_OUT: "FILE_DEVICE_MIDI_OUT",
    FILE_DEVICE_MOUSE: "FILE_DEVICE_MOUSE",
    FILE_DEVICE_MULTI_UNC_PROVIDER: "FILE_DEVICE_MULTI_UNC_PROVIDER",
    FILE_DEVICE_NAMED_PIPE: "FILE_DEVICE_NAMED_PIPE",
    FILE_DEVICE_NETWORK: "FILE_DEVICE_NETWORK",
    FILE_DEVICE_NETWORK_BROWSER: "FILE_DEVICE_NETWORK_BROWSER",
    FILE_DEVICE_NETWORK_FILE_SYSTEM: "FILE_DEVICE_NETWORK_FILE_SYSTEM",
    FILE_DEVICE_NULL: "FILE_DEVICE_NULL",
    FILE_DEVICE_PARALLEL_PORT: "FILE_DEVICE_PARALLEL_PORT",
    FILE_DEVICE_PHYSICAL_NETCARD: "FILE_DEVICE_PHYSICAL_NETCARD",
    FILE_DEVICE_PRINTER: "FILE_DEVICE_PRINTER",
    FILE_DEVICE_SCANNER: "FILE_DEVICE_SCANNER",
    FILE_DEVICE_SERIAL_MOUSE_PORT: "FILE_DEVICE_SERIAL_MOUSE_PORT",
    FILE_DEVICE_SERIAL_PORT: "FILE_DEVICE_SERIAL_PORT",
    FILE_DEVICE_SCREEN: "FILE_DEVICE_SCREEN",
    FILE_DEVICE_SOUND: "FILE_DEVICE_SOUND",
    FILE_DEVICE_STREAMS: "FILE_DEVICE_STREAMS",
    FILE_DEVICE_TAPE: "FILE_DEVICE_TAPE",
    FILE_DEVICE_TAPE_FILE_SYSTEM: "FILE_DEVICE_TAPE_FILE_SYSTEM",
    FILE_DEVICE_TRANSPORT: "FILE_DEVICE_TRANSPORT",
    FILE_DEVICE_UNKNOWN: "FILE_DEVICE_UNKNOWN",
    FILE_DEVICE_VIDEO: "FILE_DEVICE_VIDEO",
    FILE_DEVICE_VIRTUAL_DISK: "FILE_DEVICE_VIRTUAL_DISK",
    FILE_DEVICE_WAVE_IN: "FILE_DEVICE_WAVE_IN",
    FILE_DEVICE_WAVE_OUT: "FILE_DEVICE_WAVE_OUT",
    FILE_DEVICE_8042_PORT: "FILE_DEVICE_8042_PORT",
    FILE_DEVICE_NETWORK_REDIRECTOR: "FILE_DEVICE_NETWORK_REDIRECTOR",
    FILE_DEVICE_BATTERY: "FILE_DEVICE_BATTERY",
    FILE_DEVICE_BUS_EXTENDER: "FILE_DEVICE_BUS_EXTENDER",
    FILE_DEVICE_MODEM: "FILE_DEVICE_MODEM",
    FILE_DEVICE_VDM: "FILE_DEVICE_VDM",
    FILE_DEVICE_MASS_STORAGE: "FILE_DEVICE_MASS_STORAGE",
    FILE_DEVICE_SMB: "FILE_DEVICE_SMB",
    FILE_DEVICE_KS: "FILE_DEVICE_KS",
    FILE_DEVICE_CHANGER: "FILE_DEVICE_CHANGER",
    FILE_DEVICE_SMARTCARD: "FILE_DEVICE_SMARTCARD",
    FILE_DEVICE_ACPI: "FILE_DEVICE_ACPI",
    FILE_DEVICE_DVD: "FILE_DEVICE_DVD",
    FILE_DEVICE_FULLSCREEN_VIDEO: "FILE_DEVICE_FULLSCREEN_VIDEO",
    FILE_DEVICE_DFS_FILE_SYSTEM: "FILE_DEVICE_DFS_FILE_SYSTEM",
    FILE_DEVICE_DFS_VOLUME: "FILE_DEVICE_DFS_VOLUME",
    FILE_DEVICE_SERENUM: "FILE_DEVICE_SERENUM",
    FILE_DEVICE_TERMSRV: "FILE_DEVICE_TERMSRV",
    FILE_DEVICE_KSEC: "FILE_DEVICE_KSEC",
    FILE_DEVICE_FIPS: "FILE_DEVICE_FIPS",
    FILE_DEVICE_INFINIBAND: "FILE_DEVICE_INFINIBAND",
    FILE_DEVICE_VMBUS: "FILE_DEVICE_VMBUS",
    FILE_DEVICE_CRYPT_PROVIDER: "FILE_DEVICE_CRYPT_PROVIDER",
    FILE_DEVICE_WPD: "FILE_DEVICE_WPD",
    FILE_DEVICE_BLUETOOTH: "FILE_DEVICE_BLUETOOTH",
    FILE_DEVICE_MT_COMPOSITE: "FILE_DEVICE_MT_COMPOSITE",
    FILE_DEVICE_MT_TRANSPORT: "FILE_DEVICE_MT_TRANSPORT",
    FILE_DEVICE_BIOMETRIC: "FILE_DEVICE_BIOMETRIC",
    FILE_DEVICE_PMI: "FILE_DEVICE_PMI"
}

# file access
FILE_ANY_ACCESS = 0
FILE_READ_DATA = 1
FILE_WRITE_DATA = 2

DEVICE_ACCESS = {
    FILE_ANY_ACCESS: "FILE_ANY_ACCESS",
    FILE_READ_DATA: "FILE_READ_DATA",
    FILE_WRITE_DATA: "FILE_WRITE_DATA",
    FILE_READ_DATA | FILE_WRITE_DATA: "FILE_READ_DATA | FILE_WRITE_DATA"
}

# Transfer Type
METHOD_BUFFERED = 0
METHOD_IN_DIRECT = 1
METHOD_OUT_DIRECT = 2
METHOD_NEITHER = 3

DEVICE_METHOD = {
    METHOD_BUFFERED: "METHOD_BUFFERED",
    METHOD_IN_DIRECT: "METHOD_IN_DIRECT",
    METHOD_OUT_DIRECT: "METHOD_OUT_DIRECT",
    METHOD_NEITHER: "METHOD_NEITHER",
}


DO_BUFFERED_IO                 = 0x00000004
DO_EXCLUSIVE                   = 0x00000008
DO_DIRECT_IO                   = 0x00000010
DO_MAP_IO_BUFFER               = 0x00000020
DO_DEVICE_INITIALIZING         = 0x00000080
DO_SHUTDOWN_REGISTERED         = 0x00000800
DO_BUS_ENUMERATED_DEVICE       = 0x00001000
DO_POWER_PAGABLE               = 0x00002000
DO_POWER_INRUSH                = 0x00004000
