#!/usr/bin/env python3
"""
Test suite for Qiling LLM Analyzer

This test suite validates:
1. Configuration management
2. LLM interface functionality
3. Report processing
4. Analysis generation
5. Output formatting
"""

import sys
import os
import json
import tempfile
import unittest
from pathlib import Path

# Add qiling to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from qiling.extensions.llm_analyzer import <PERSON>l<PERSON>MAnaly<PERSON>, QlReportEnhancer, LLMConfig
from qiling.extensions.llm_analyzer.config_manager import ConfigManager
from qiling.extensions.llm_analyzer.templates import get_template, list_templates


class TestLLMConfig(unittest.TestCase):
    """Test LLM configuration functionality"""
    
    def test_default_config(self):
        """Test default configuration creation"""
        config = LLMConfig()
        
        self.assertEqual(config.backend, "ollama")
        self.assertEqual(config.model, "llama3.2:3b")
        self.assertEqual(config.base_url, "http://localhost:11434")
        self.assertIsInstance(config.temperature, float)
        self.assertIsInstance(config.max_tokens, int)
    
    def test_config_serialization(self):
        """Test configuration serialization/deserialization"""
        config = LLMConfig(
            backend="test_backend",
            model="test_model",
            temperature=0.5,
            max_tokens=2048
        )
        
        # Test to_dict
        config_dict = config.to_dict()
        self.assertEqual(config_dict["backend"], "test_backend")
        self.assertEqual(config_dict["model"], "test_model")
        self.assertEqual(config_dict["temperature"], 0.5)
        
        # Test from_dict
        new_config = LLMConfig.from_dict(config_dict)
        self.assertEqual(new_config.backend, config.backend)
        self.assertEqual(new_config.model, config.model)
        self.assertEqual(new_config.temperature, config.temperature)
    
    def test_config_file_operations(self):
        """Test configuration file save/load"""
        config = LLMConfig(backend="file_test", model="test_model")
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_path = f.name
        
        try:
            # Test save
            config.save_to_file(temp_path)
            self.assertTrue(os.path.exists(temp_path))
            
            # Test load
            loaded_config = LLMConfig.from_file(temp_path)
            self.assertEqual(loaded_config.backend, "file_test")
            self.assertEqual(loaded_config.model, "test_model")
            
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)


class TestConfigManager(unittest.TestCase):
    """Test configuration manager functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = ConfigManager(self.temp_dir)
    
    def tearDown(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_template_listing(self):
        """Test template listing functionality"""
        templates = self.config_manager.list_templates()
        
        self.assertIsInstance(templates, list)
        self.assertGreater(len(templates), 0)
        
        # Check template structure
        for template in templates:
            self.assertIn("name", template)
            self.assertIn("description", template)
            self.assertIn("settings", template)
    
    def test_config_creation(self):
        """Test configuration creation from templates"""
        config = self.config_manager.create_config("test_config", "ollama_local")
        
        self.assertIsInstance(config, LLMConfig)
        self.assertEqual(config.backend, "ollama")
        
        # Check if config was saved
        configs = self.config_manager.list_configs()
        self.assertIn("test_config", configs)
    
    def test_config_validation(self):
        """Test configuration validation"""
        # Valid config
        valid_config = LLMConfig(
            backend="ollama",
            model="test_model",
            base_url="http://localhost:11434"
        )
        
        validation = self.config_manager.validate_config(valid_config)
        self.assertTrue(validation["valid"])
        self.assertEqual(len(validation["errors"]), 0)
        
        # Invalid config
        invalid_config = LLMConfig(
            backend="",  # Empty backend
            model="",    # Empty model
            base_url=""  # Empty URL
        )
        
        validation = self.config_manager.validate_config(invalid_config)
        self.assertFalse(validation["valid"])
        self.assertGreater(len(validation["errors"]), 0)


class TestAnalysisTemplates(unittest.TestCase):
    """Test analysis template functionality"""
    
    def test_template_listing(self):
        """Test template listing"""
        templates = list_templates()
        
        self.assertIsInstance(templates, list)
        self.assertGreater(len(templates), 0)
        
        # Check for expected templates
        expected_templates = [
            "comprehensive_analysis",
            "security_assessment",
            "behavior_analysis",
            "malware_analysis"
        ]
        
        for expected in expected_templates:
            self.assertIn(expected, templates)
    
    def test_template_retrieval(self):
        """Test template retrieval"""
        template = get_template("comprehensive_analysis")
        
        self.assertIsNotNone(template)
        self.assertEqual(template.name, "Comprehensive Analysis")
        self.assertIsInstance(template.system_prompt, str)
        self.assertIsInstance(template.user_prompt_template, str)
        self.assertEqual(template.analysis_type, "general")
    
    def test_template_formatting(self):
        """Test template prompt formatting"""
        template = get_template("security_assessment")
        
        test_data = {
            "filename": "test.exe",
            "arch": "x86_64",
            "os": "windows",
            "syscalls_summary": "Test syscalls",
            "strings_summary": "Test strings",
            "registry_summary": "Test registry",
            "symbols_summary": "Test symbols",
            "network_indicators": "Test network",
            "entry_point": "0x401000"
        }
        
        formatted_prompt = template.format_prompt(test_data)
        
        self.assertIn("test.exe", formatted_prompt)
        self.assertIn("x86_64", formatted_prompt)
        self.assertIn("windows", formatted_prompt)
        self.assertIn("Test syscalls", formatted_prompt)


class TestReportProcessing(unittest.TestCase):
    """Test report processing functionality"""
    
    def setUp(self):
        """Set up test environment"""
        # Create mock LLM config that won't try to connect
        self.config = LLMConfig(
            backend="mock",
            model="mock_model",
            base_url="http://localhost:9999"  # Non-existent port
        )
    
    def test_report_data_processing(self):
        """Test report data processing"""
        # Create mock analyzer (won't initialize LLM)
        analyzer = QlLLMAnalyzer.__new__(QlLLMAnalyzer)
        analyzer.config = self.config
        analyzer.llm_interface = None
        
        # Test data processing
        mock_report = {
            "filename": "test.exe",
            "arch": "x86_64",
            "os": "windows",
            "strings": ["test string 1", "http://example.com", "C:\\Windows\\System32"],
            "syscalls": {
                "CreateFileA": [
                    {"params": {"filename": "test.txt"}, "retval": 123}
                ],
                "WriteFile": [
                    {"params": {"handle": 123, "data": "test"}, "retval": 1}
                ]
            }
        }
        
        processed = analyzer._process_report_data(mock_report)
        
        self.assertEqual(processed["filename"], "test.exe")
        self.assertEqual(processed["arch"], "x86_64")
        self.assertEqual(processed["os"], "windows")
        self.assertIn("CreateFileA", processed["syscalls_summary"])
        self.assertIn("WriteFile", processed["syscalls_summary"])
        self.assertIn("http://example.com", processed["strings_summary"])
    
    def test_network_indicator_extraction(self):
        """Test network indicator extraction"""
        analyzer = QlLLMAnalyzer.__new__(QlLLMAnalyzer)
        analyzer.config = self.config
        
        mock_report = {
            "strings": [
                "http://malicious.com",
                "tcp://192.168.1.1:8080",
                "normal string",
                "socket connection"
            ],
            "syscalls": {
                "socket": [{"params": {}, "retval": 1}],
                "connect": [{"params": {}, "retval": 0}],
                "CreateFileA": [{"params": {}, "retval": 123}]
            }
        }
        
        indicators = analyzer._extract_network_indicators(mock_report)
        
        self.assertIn("http://malicious.com", indicators)
        self.assertIn("socket", indicators)
        self.assertIn("connect", indicators)


class TestSystemIntegration(unittest.TestCase):
    """Test system integration (requires running LLM backend)"""
    
    def setUp(self):
        """Set up integration test environment"""
        self.config = LLMConfig(
            backend="ollama",
            model="llama3.2:3b",
            base_url="http://localhost:11434"
        )
        
        # Check if LLM backend is available
        try:
            import requests
            response = requests.get(f"{self.config.base_url}/api/tags", timeout=2)
            self.llm_available = response.status_code == 200
        except:
            self.llm_available = False
    
    def test_analyzer_initialization(self):
        """Test analyzer initialization"""
        analyzer = QlLLMAnalyzer(self.config)
        
        if self.llm_available:
            self.assertTrue(analyzer.is_ready())
        else:
            self.assertFalse(analyzer.is_ready())
    
    @unittest.skipUnless(os.environ.get('RUN_LLM_TESTS'), "LLM tests require RUN_LLM_TESTS=1")
    def test_full_analysis_workflow(self):
        """Test full analysis workflow (requires LLM backend)"""
        if not self.llm_available:
            self.skipTest("LLM backend not available")
        
        analyzer = QlLLMAnalyzer(self.config)
        
        # Mock report data
        mock_report = {
            "filename": "test_sample.exe",
            "arch": "x86_64",
            "os": "windows",
            "strings": ["Hello World", "kernel32.dll"],
            "syscalls": {
                "CreateFileA": [{"params": {"filename": "test.txt"}, "retval": 123}]
            }
        }
        
        # Run analysis
        result = analyzer.analyze_report(mock_report, "behavior_analysis")
        
        # Validate result structure
        self.assertIn("metadata", result)
        self.assertIn("analysis", result)
        self.assertIn("content", result["analysis"])
        
        # Check metadata
        metadata = result["metadata"]
        self.assertIn("analysis_timestamp", metadata)
        self.assertIn("template_used", metadata)
        self.assertEqual(metadata["template_used"], "behavior_analysis")


def run_system_validation():
    """Run comprehensive system validation"""
    print("=== Qiling LLM Analyzer System Validation ===\n")
    
    # Check 1: Configuration system
    print("1. Testing configuration system...")
    try:
        config_manager = ConfigManager()
        templates = config_manager.list_templates()
        print(f"   ✓ Found {len(templates)} configuration templates")
        
        # Test config creation
        test_config = config_manager.create_config("validation_test", "ollama_local")
        print(f"   ✓ Configuration creation successful")
        
        # Clean up
        config_manager.delete_config("validation_test")
        
    except Exception as e:
        print(f"   ✗ Configuration system error: {e}")
    
    # Check 2: Template system
    print("\n2. Testing template system...")
    try:
        templates = list_templates()
        print(f"   ✓ Found {len(templates)} analysis templates")
        
        for template_name in templates[:3]:  # Test first 3 templates
            template = get_template(template_name)
            print(f"   ✓ Template '{template_name}' loaded successfully")
            
    except Exception as e:
        print(f"   ✗ Template system error: {e}")
    
    # Check 3: LLM backend connectivity
    print("\n3. Testing LLM backend connectivity...")
    try:
        import requests
        
        backends = [
            ("Ollama", "http://localhost:11434/api/tags"),
            ("llama.cpp", "http://localhost:8080/health"),
            ("LM Studio", "http://localhost:1234/v1/models")
        ]
        
        available_count = 0
        for name, url in backends:
            try:
                response = requests.get(url, timeout=2)
                if response.status_code == 200:
                    print(f"   ✓ {name} backend available")
                    available_count += 1
                else:
                    print(f"   ✗ {name} backend returned status {response.status_code}")
            except:
                print(f"   ✗ {name} backend not accessible")
        
        if available_count == 0:
            print("   ⚠ No LLM backends available - analysis will not work")
        else:
            print(f"   ✓ {available_count} LLM backend(s) available")
            
    except Exception as e:
        print(f"   ✗ Backend connectivity test error: {e}")
    
    # Check 4: Analyzer initialization
    print("\n4. Testing analyzer initialization...")
    try:
        config = LLMConfig()
        analyzer = QlLLMAnalyzer(config)
        
        if analyzer.is_ready():
            print("   ✓ LLM analyzer ready for use")
        else:
            print("   ⚠ LLM analyzer not ready (check backend availability)")
            
    except Exception as e:
        print(f"   ✗ Analyzer initialization error: {e}")
    
    print("\n=== Validation Complete ===")
    print("Run 'python -m unittest tests.test_llm_analyzer' for detailed unit tests")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--validate":
        run_system_validation()
    else:
        unittest.main()
