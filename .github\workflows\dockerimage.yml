name: Docker Image CI

on:
  release:
    types: [published]
  push:
    branches: [dev]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: "Checkout git repo"
        uses: actions/checkout@v1

      - name: Publish to registry
        uses: docker/build-push-action@v1.1.0
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
          repository: qilingframework/qiling
          tag_with_ref: true
          tags: latest
