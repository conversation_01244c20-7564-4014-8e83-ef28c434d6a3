#!/usr/bin/env python3
#
# Cross Platform and Multi Architecture Advanced Binary Emulation Framework
# LLM-powered Analysis Extension
#

from .config import LLMConfig, get_default_config
from .analyzer import QlLLMAnalyzer
from .enhancer import QlReportEnhancer
from .templates import AnalysisTemplate, get_template, list_templates
from .config_manager import Config<PERSON>anager
from .llm_interface import create_llm_interface, LLMInterface

__version__ = "1.0.0"

__all__ = [
    # Core classes
    'QlLLMAnalyzer',
    'QlReportEnhancer',

    # Configuration
    'LLMConfig',
    'get_default_config',
    'ConfigManager',

    # Templates
    'AnalysisTemplate',
    'get_template',
    'list_templates',

    # LLM Interface
    'create_llm_interface',
    'LLMInterface',

    # Version
    '__version__'
]

# Convenience imports for common use cases
def quick_analyze(binary_path, rootfs_path, template="comprehensive_analysis",
                 backend="ollama", model="llama3.2:3b"):
    """Quick analysis function for simple use cases

    Args:
        binary_path: Path to binary to analyze
        rootfs_path: Path to rootfs directory
        template: Analysis template to use
        backend: LLM backend to use
        model: LLM model to use

    Returns:
        Analysis result dictionary
    """
    from qiling import Qiling

    # Configure LLM
    config = LLMConfig(backend=backend, model=model)
    analyzer = QlLLMAnalyzer(config)

    if not analyzer.is_ready():
        raise RuntimeError("LLM analyzer not ready. Check backend availability.")

    # Run Qiling
    ql = Qiling([binary_path], rootfs_path, verbose=False)
    ql.run()

    # Analyze
    return analyzer.analyze_qiling_instance(ql, template)


def quick_enhance(binary_path, rootfs_path, enhancement_types=None):
    """Quick report enhancement for simple use cases

    Args:
        binary_path: Path to binary to analyze
        rootfs_path: Path to rootfs directory
        enhancement_types: List of enhancement types

    Returns:
        Enhanced report dictionary
    """
    from qiling import Qiling

    if enhancement_types is None:
        enhancement_types = ['summary', 'security', 'behavior', 'recommendations']

    # Initialize enhancer
    enhancer = QlReportEnhancer()

    if not enhancer.analyzer.is_ready():
        raise RuntimeError("LLM enhancer not ready. Check backend availability.")

    # Run analysis and enhance
    return enhancer.enhance_qiling_instance(
        Qiling([binary_path], rootfs_path, verbose=False),
        enhancement_types
    )
